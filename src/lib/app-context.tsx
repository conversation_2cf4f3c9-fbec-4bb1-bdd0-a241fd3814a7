import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './auth-context';
import { getInitialWorkspaceDataForUser, getMessagesForChannel, getMessagesForDirectMessage, getUserConversationReadStates } from './supabase-data-provider'; // Added getMessagesForDirectMessage and getUserConversationReadStates
import { initializeDatabase, switchDatabaseUser } from './db';
import { localStorageCache } from './localStorage-cache';
import { DirectMessage, Message, Section, Thread, Channel, User, Workspace, UserSettings, WorkspaceSettings, ChannelViewKey, WorkspaceDisplayUser, PersistentUnreadInfo, UserConversationReadState, ChannelTopic, LocalAttachment } from './types'; // Added ChannelTopic, LocalAttachment
import { deduplicateDirectMessages, findSectionById, findChannelById, transformSupabaseMessage, findOrCreateThreadFromContext, updateOptimisticMessageInArray, mergeWorkspaceData, applyRealtimeMessageToWorkspace, applyOptimisticMessageUpdate, revertOptimisticMessageUpdate, applyReactionUpdateToWorkspace, applyAddChannelUpdateToWorkspace, applyAddSectionUpdateToWorkspace, applyAddDirectMessageUpdateToWorkspace, applyUpdateChannelToWorkspace, applyMarkConversationReadToWorkspace, applyUpdateUserSettingToWorkspace, applyUpdateWorkspaceSettingsToWorkspace, applyUpdateUserStatusToWorkspace, applyNavigateToChannelTopicToWorkspace, applyClearActiveChannelTopicToWorkspace, applySetCurrentSectionToWorkspace, applySetCurrentChannelToWorkspace, applySetCurrentDirectMessageToWorkspace, applySetActiveThreadToWorkspace, organizeMessagesIntoThreads } from './app-context-utils'; // Added utility imports
import { MemberProfileDialog } from '@/components/MemberProfileDialog';
import { supabase } from './supabaseClient'; // For sendMessage
import { generateDisplayId } from './utils'; // Import generateDisplayId
import { toast } from 'sonner';
import { handleSupabaseError, handleUnexpectedError, OPERATION_CONTEXTS } from './error-utils'; // Import toast for notifications
import { AppLoadingScreen } from '@/components/AppLoadingScreen';
import { useVisibilityChange } from '@/hooks/use-visibility-change';

// Default message fetch limits (used as fallbacks when workspace settings are not available)
const DEFAULT_INITIAL_MESSAGE_FETCH_LIMIT = 25;
const DEFAULT_OLDER_MESSAGE_FETCH_LIMIT = 25;
const DEFAULT_DELTA_MESSAGE_FETCH_LIMIT = 50;

export type NavigationItem = { type: 'channel'; id: string } | { type: 'dm'; id: string };

interface AppContextType {
  workspace: Workspace | null;
  currentSection: Section | null;
  currentChannel: Channel | null;
  currentDirectMessage: DirectMessage | null;
  currentThread: Thread | null;
  setCurrentSection: (sectionId: string | null) => void;
  setCurrentChannel: (channelId: string | null, fromNavigation?: boolean, fromTopicNavigation?: boolean) => void;
  setCurrentDirectMessage: (directMessageId: string | null, fromNavigation?: boolean) => void;
  setActiveThread: (threadId: string | null) => void;
  sendMessage: (content: string, channelId?: string, directMessageId?: string, threadId?: string, topicId?: string, attachmentsData?: LocalAttachment[]) => Promise<void>; // Made async, added attachmentsData
  getCurrentUser: () => User;
  toggleSidebar: () => void;
  isSidebarOpen: boolean;
  addReaction: (messageId: string, emoji: string) => void;
  addChannel: (name: string, sectionId: string, isPrivate?: boolean) => void;
  addSection: (name: string) => Promise<Section | null>; // Modified to be async and return new section or null
  updateSection: (sectionId: string, updates: Partial<Pick<Section, 'name' | 'display_order'>>) => Promise<Section | null>;
  deleteSection: (sectionId: string) => Promise<boolean>;
  addDirectMessage: (userId: string) => Promise<string | null>; // Made async, returns DM session ID or null
  switchWorkspace: (workspaceId: string) => Promise<void>;
  updateChannel: (updates: Partial<Channel> & Pick<Channel, 'id'>) => Promise<void>;
  conversationReadMarkers: Record<string, string | undefined>;
  markConversationRead: (conversationId: string, type: 'channel' | 'dm') => void;
  effectiveMarkAsReadDelaySeconds: number;
  updateUserSetting: (userId: string, newSettings: UserSettings) => Promise<void>;
  workspaceSettings?: WorkspaceSettings;
  updateWorkspaceSettings: (workspaceId: string, newSettings: Partial<WorkspaceSettings>) => void;
  updateUserStatus: (userId: string, status: User['status'], statusMessage?: string) => Promise<void>;
  updateUserProfile: (userId: string, profileData: { name?: string; title?: string; about?: string; avatar?: string }) => Promise<void>;
  searchHistory: string[];
  searchResults: Message[];
  performSearch: (term: string) => void;
  addSearchToHistory: (term: string) => void;
  clearSearchHistory: () => void;
  clearSearchResults: () => void;
  navigationHistory: NavigationItem[];
  currentHistoryIndex: number;
  canGoBack: boolean;
  canGoForward: boolean;
  navigateBack: () => void;
  navigateForward: () => void;
  navigateTo: (item: NavigationItem) => void;
  recentConversations: NavigationItem[];
  isSearchViewActive: boolean;
  setIsSearchViewActive: (isActive: boolean) => void;
  showMemberProfile: (userId: string) => void;
  navigateToChannelTopic: (channelId: string, topicId: string) => void;
  currentChannelActiveView: ChannelViewKey | null;
  setCurrentChannelActiveView: (view: ChannelViewKey | null) => void;
  clearActiveChannelTopicForChannel: (channelId: string) => void;
  autoResetNewMessages: boolean;
  setAutoResetNewMessages: (value: boolean) => void;
  persistentUnreadInfo: PersistentUnreadInfo;
  setPersistentUnreadInfo: React.Dispatch<React.SetStateAction<PersistentUnreadInfo>>;
  loadOlderMessages: (conversationId: string, conversationType: 'channel' | 'dm') => Promise<void>;
  fetchNewerMessagesForConversation: (conversationId: string, conversationType: 'channel' | 'dm') => Promise<void>;
  createChannelTopic: (channelId: string, title: string, summary?: string) => Promise<ChannelTopic | null>;
  updateChannelTopic: (topicId: string, updates: { title?: string; summary?: string }) => Promise<ChannelTopic | null>;
  deleteChannelTopic: (topicId: string, channelId: string) => Promise<boolean>;
  archiveChannelTopic: (topicId: string, channelId: string) => Promise<ChannelTopic | null>;
  unarchiveChannelTopic: (topicId: string, channelId: string) => Promise<ChannelTopic | null>;
  createWorkspace: (name: string, iconUrl?: string) => Promise<Workspace | null>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: React.ReactNode }) {
  const { user: authUser, profile: authProfile, session, loading: authLoading } = useAuth();
  const [workspace, setWorkspace] = useState<Workspace | null>(null);
  const [workspaceLoading, setWorkspaceLoading] = useState(true);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [conversationReadMarkers, setConversationReadMarkers] = useState<Record<string, string | undefined>>({});
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [searchResults, setSearchResults] = useState<Message[]>([]);
  const [navigationHistory, setNavigationHistory] = useState<NavigationItem[]>([]);
  const [currentHistoryIndex, setCurrentHistoryIndex] = useState<number>(-1);
  const [isSearchViewActive, setIsSearchViewActive] = useState<boolean>(false);
  const [memberProfileDialogOpen, setMemberProfileDialogOpen] = useState<boolean>(false);
  const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null);
  const [currentChannelActiveView, setCurrentChannelActiveView] = useState<ChannelViewKey | null>(null);
  const [autoResetNewMessages, setAutoResetNewMessagesState] = useState<boolean>(true);
  const [persistentUnreadInfo, setPersistentUnreadInfo] = useState<PersistentUnreadInfo>({});
  const [isPuiLoaded, setIsPuiLoaded] = useState(false); // New state for PUI loading status
  const prevVisibilityStateRef = useRef(document.visibilityState);

  // Track visibility changes for better loading experience
  const { isReturningToVisible, loadingAfterReturn, resetLoadingState } = useVisibilityChange();

  // Helper functions to get message fetch limits from workspace settings
  const getInitialMessageFetchLimit = () => {
    return workspace?.settings?.initialMessageFetchLimit ?? DEFAULT_INITIAL_MESSAGE_FETCH_LIMIT;
  };

  const getOlderMessageFetchLimit = () => {
    return workspace?.settings?.olderMessageFetchLimit ?? DEFAULT_OLDER_MESSAGE_FETCH_LIMIT;
  };

  const getDeltaMessageFetchLimit = () => {
    return workspace?.settings?.deltaMessageFetchLimit ?? DEFAULT_DELTA_MESSAGE_FETCH_LIMIT;
  };

  const applyInitialUnreadCountsToWorkspaceData = (
    wsData: Workspace,
    pui: PersistentUnreadInfo,
    serverReadStates: UserConversationReadState[] | null
  ): Workspace => {
    const updatedWsData = JSON.parse(JSON.stringify(wsData)) as Workspace; // Deep clone
    // console.log('[UnreadCalc] Applying initial unread counts. Server states:', serverReadStates, 'PUI:', pui);

    if (updatedWsData.sections) {
      updatedWsData.sections.forEach((section: Section) => {
        section.channels.forEach((channel: Channel) => {
          const conversationId = channel.id;
          const dbTimestampStr = channel.lastMessageTimestamp;
          let initialUnreadCount = 0;
          let isPlaceholder = false;
          let usedSource = "fallback (no db timestamp)";

          const serverState = serverReadStates?.find(srs => srs.conversation_id === conversationId);

          if (serverState && serverState.last_read_message_timestamp) {
            usedSource = "server";
            if (dbTimestampStr) {
              const dbDate = new Date(dbTimestampStr);
              const serverReadDate = new Date(serverState.last_read_message_timestamp);
              if (dbDate > serverReadDate) {
                initialUnreadCount = 1;
                isPlaceholder = false;
              } else {
                initialUnreadCount = 0;
                isPlaceholder = false;
              }
            } else {
              initialUnreadCount = 0;
              isPlaceholder = false;
            }
          } else {
            // Fallback to PUI (localStorage)
            const storedInfo = pui[conversationId];
            if (storedInfo && storedInfo.lastReadMessageTimestamp) {
              usedSource = "localStorage";
              if (dbTimestampStr) {
                const dbDate = new Date(dbTimestampStr);
                const storedDate = new Date(storedInfo.lastReadMessageTimestamp);
                if (dbDate > storedDate) {
                  initialUnreadCount = 1;
                  isPlaceholder = false;
                } else {
                  initialUnreadCount = 0;
                  isPlaceholder = false;
                }
              } else {
                initialUnreadCount = 0;
                isPlaceholder = false;
              }
              // console.log(`[UnreadCalc CH ${conversationId}] No server state, using PUI. DB: ${dbTimestampStr}, PUIRead: ${storedInfo.lastReadMessageTimestamp}. Unread: ${initialUnreadCount}, Placeholder: ${isPlaceholder}`);
            } else {
              // No server state and no PUI, or PUI has no timestamp
              usedSource = dbTimestampStr ? "localStorage (no PUI entry, has DB timestamp)" : "localStorage (no PUI entry, no DB timestamp)";
              if (dbTimestampStr) {
                  initialUnreadCount = 1;
                  isPlaceholder = true; // This is the "Case A"
              } else {
                  initialUnreadCount = 0;
                  isPlaceholder = false;
              }
              // console.log(`[UnreadCalc CH ${conversationId}] No server state, no/invalid PUI. DB: ${dbTimestampStr}. Unread: ${initialUnreadCount}, Placeholder: ${isPlaceholder}`);
            }
          }
          channel.unreadCount = initialUnreadCount;
          channel.isUnreadCountPlaceholder = isPlaceholder && initialUnreadCount === 1;
          channel.oldestFetchedMessageTimestamp = null; // Initialize pagination flag
          channel.oldestFetchedMessageId = null; // Keyset pagination
          channel.newestFetchedMessageId = null; // Keyset pagination (delta)
          channel.hasMoreOlderMessages = !!channel.lastMessageTimestamp; // Initialize based on whether any message has ever existed
          channel.last_fetched_message_timestamp = null; // Initialize delta sync timestamp
          // console.log(`[UnreadCalc] Channel ${channel.name} (${conversationId}) unread: ${initialUnreadCount}, Placeholder: ${channel.isUnreadCountPlaceholder} (Source: ${usedSource})`);
        });
      });
    }

    if (updatedWsData.directMessages) {
      updatedWsData.directMessages.forEach((dm: DirectMessage) => {
        const conversationId = dm.id;
        const dbTimestampStr = dm.lastMessageTimestamp;
        let initialUnreadCount = 0;
        let isPlaceholder = false;
        let usedSource = "fallback (no db timestamp)";

        const serverState = serverReadStates?.find(srs => srs.conversation_id === conversationId);

        if (serverState && serverState.last_read_message_timestamp) {
          usedSource = "server";
          if (dbTimestampStr) {
            const dbDate = new Date(dbTimestampStr);
            const serverReadDate = new Date(serverState.last_read_message_timestamp);
            if (dbDate > serverReadDate) {
              initialUnreadCount = 1;
              isPlaceholder = false;
            } else {
              initialUnreadCount = 0;
              isPlaceholder = false;
            }
          } else {
            initialUnreadCount = 0;
            isPlaceholder = false;
          }

        } else {
          const storedInfo = pui[conversationId];
          if (storedInfo && storedInfo.lastReadMessageTimestamp) {
            usedSource = "localStorage";
            if (dbTimestampStr) {
              const dbDate = new Date(dbTimestampStr);
              const storedDate = new Date(storedInfo.lastReadMessageTimestamp);
              if (dbDate > storedDate) {
                initialUnreadCount = 1;
                isPlaceholder = false;
              } else {
                initialUnreadCount = 0;
                isPlaceholder = false;
              }
            } else {
              initialUnreadCount = 0;
              isPlaceholder = false;
            }
            // console.log(`[UnreadCalc DM ${conversationId}] No server state, using PUI. DB: ${dbTimestampStr}, PUIRead: ${storedInfo.lastReadMessageTimestamp}. Unread: ${initialUnreadCount}, Placeholder: ${isPlaceholder}`);
          } else {
            usedSource = dbTimestampStr ? "localStorage (no PUI entry, has DB timestamp)" : "localStorage (no PUI entry, no DB timestamp)";
            if (dbTimestampStr) {
                initialUnreadCount = 1;
                isPlaceholder = true; // This is the "Case A"
            } else {
                initialUnreadCount = 0;
                isPlaceholder = false;
            }
            // console.log(`[UnreadCalc DM ${conversationId}] No server state, no/invalid PUI. DB: ${dbTimestampStr}. Unread: ${initialUnreadCount}, Placeholder: ${isPlaceholder}`);
          }
        }
        dm.unreadCount = initialUnreadCount;
        dm.isUnreadCountPlaceholder = isPlaceholder && initialUnreadCount === 1;
        dm.oldestFetchedMessageTimestamp = null; // Initialize pagination flag
        dm.oldestFetchedMessageId = null; // Keyset pagination
        dm.newestFetchedMessageId = null; // Keyset pagination (delta)
        dm.hasMoreOlderMessages = !!dm.lastMessageTimestamp; // Initialize based on whether any message has ever existed
        dm.last_fetched_message_timestamp = null; // Initialize delta sync timestamp
        // console.log(`[UnreadCalc] DM ${dm.id} unread: ${initialUnreadCount}, Placeholder: ${dm.isUnreadCountPlaceholder} (Source: ${usedSource})`);
      });
    }
    return updatedWsData;
  };

  // Track IndexedDB availability
  const [indexedDBAvailable, setIndexedDBAvailable] = useState<boolean>(true);

  // Initialize database when user changes
  useEffect(() => {
    const initDB = async () => {
      const dbInitialized = await initializeDatabase(authUser?.id);
      setIndexedDBAvailable(dbInitialized);

      if (!dbInitialized) {
        // toast.info("Using simplified caching mode due to browser storage issues.", {
        //   duration: 5000,
        // });
      }
    };

    // Initialize database when auth user changes (including login/logout)
    if (!authLoading) {
      initDB();
    }
  }, [authUser?.id, authLoading]); // Re-initialize when user changes

  useEffect(() => {
    const loadWorkspaceData = async () => {
      if (authUser && authProfile) { // This is line 148 after additions
        setWorkspaceLoading(true);
        let loadedWorkspace: Workspace | null = null;
        let serverReadStates: UserConversationReadState[] | null = null;

        try {
          // Fetch workspace data and server read states concurrently
          const [fetchedWorkspaceResult, serverReadStatesResult] = await Promise.allSettled([
            getInitialWorkspaceDataForUser(authUser.id),
            getUserConversationReadStates(authUser.id)
          ]);

          if (fetchedWorkspaceResult.status === 'fulfilled' && fetchedWorkspaceResult.value) {
            loadedWorkspace = fetchedWorkspaceResult.value;
          } else {
            console.error("Error fetching initial workspace data:", fetchedWorkspaceResult.status === 'rejected' ? fetchedWorkspaceResult.reason : "No data returned");
            // loadedWorkspace will remain null if the fetch failed or returned null
          }

          if (serverReadStatesResult.status === 'fulfilled' && serverReadStatesResult.value) {
            serverReadStates = serverReadStatesResult.value;
            // console.log("[app-context] Successfully fetched server read states:", serverReadStates);
          } else {
            console.warn("Error fetching server read states, will fallback to localStorage for unread counts:", serverReadStatesResult.status === 'rejected' ? serverReadStatesResult.reason : "No data returned");
            serverReadStates = null; // Ensure it's null on error
          }

        } catch (fetchError) { // Catch any other unforeseen errors from Promise.allSettled or setup
          console.error("Unexpected error during initial data fetch:", fetchError);
          // loadedWorkspace will remain null
        }

        if (loadedWorkspace) {
          if (loadedWorkspace.directMessages && loadedWorkspace.directMessages.length > 0) {
            const dedupedDMs = deduplicateDirectMessages(loadedWorkspace.directMessages, authUser.id);
            if (dedupedDMs.length !== loadedWorkspace.directMessages.length) {
              // console.log(`[app-context] Removed ${loadedWorkspace.directMessages.length - dedupedDMs.length} duplicate DM entries`);
              loadedWorkspace.directMessages = dedupedDMs;
            }
          }
          // Apply initial unread counts using serverReadStates and pui
          loadedWorkspace = applyInitialUnreadCountsToWorkspaceData(loadedWorkspace, persistentUnreadInfo, serverReadStates);
          setWorkspace(prevWorkspace => mergeWorkspaceData(prevWorkspace, loadedWorkspace));
        } else {
          // Handle failure to load initial workspace data from both network and cache
          console.error("Failed to load workspace data from backend. No fallback available.");
          toast.error("Failed to load workspace data. Please check your connection and try reloading.");
          setWorkspace(null);
        }
        setWorkspaceLoading(false);
      } else if (!authLoading && !session) {
        setWorkspace(null);
        setWorkspaceLoading(false);
      }
    };

    // Only load workspace data if auth is settled AND persistent unread info is loaded
    if (!authLoading && authUser && authProfile && session && isPuiLoaded) {
      loadWorkspaceData();
    } else if (!authLoading && !session) { // Handle logout case: clear workspace
      setWorkspace(null);
      setWorkspaceLoading(false);
    }
  }, [authUser, authProfile, session, authLoading, isPuiLoaded]); // Dependencies updated

  // Load persistentUnreadInfo from localStorage when authUser is available
  useEffect(() => {
    if (authUser?.id) {
      // Set current user for localStorage cache isolation
      localStorageCache.setCurrentUser(authUser.id);

      setIsPuiLoaded(false); // Reset loading status for new user
      const localStorageKey = `unreadStatus_${authUser.id}`;
      try {
        const storedInfo = localStorage.getItem(localStorageKey);
        // console.log('[app-context] LOADED PUI string from LS:', storedInfo); // Added log
        if (storedInfo) {
          const parsedInfo = JSON.parse(storedInfo) as PersistentUnreadInfo;
          // console.log('[app-context] PARSED PUI from LS:', parsedInfo); // Added log
          setPersistentUnreadInfo(parsedInfo);
          // console.log('[app-context] Loaded persistentUnreadInfo from localStorage:', parsedInfo);
        } else {
          // console.log('[app-context] No PUI string in LS, will initialize to empty.'); // Added log for else case
          setPersistentUnreadInfo({}); // Initialize if nothing is stored
          // console.log('[app-context] No persistentUnreadInfo in localStorage, initialized to empty.');
        }
      } catch (error) {
        console.error("Error loading persistentUnreadInfo from localStorage:", error);
        setPersistentUnreadInfo({}); // Initialize on error
      } finally {
        setIsPuiLoaded(true); // Mark as loaded regardless of outcome
      }
    } else {
      // Clear persistent info if user logs out or is not available
      localStorageCache.setCurrentUser(null); // Clear current user from cache
      setPersistentUnreadInfo({});
      setIsPuiLoaded(false); // Not loaded if no user
    }
  }, [authUser?.id]); // Depend on authUser.id

  // Save persistentUnreadInfo to localStorage when it changes
  // Save persistentUnreadInfo to localStorage when it or authUser.id changes
  useEffect(() => {
    if (authUser?.id) { // Only proceed if there is an authenticated user
      const localStorageKey = `unreadStatus_${authUser.id}`;
      try {
        if (Object.keys(persistentUnreadInfo).length === 0) {
          localStorage.setItem(localStorageKey, JSON.stringify(persistentUnreadInfo));
          // console.log('[app-context] Saved empty persistentUnreadInfo as "{}" to localStorage.');
        } else {
          // console.log('[app-context] SAVING NON-EMPTY PUI to LS:', persistentUnreadInfo);
          localStorage.setItem(localStorageKey, JSON.stringify(persistentUnreadInfo));
        }
      } catch (error) {
        console.error("Error saving persistentUnreadInfo to localStorage:", error);
      }
    }
  }, [persistentUnreadInfo, authUser?.id]); // Depend on persistentUnreadInfo and authUser.id

  useEffect(() => {
    if (!workspace) return;
    const storedSearchHistory = localStorage.getItem('searchHistory');
    if (storedSearchHistory) setSearchHistory(JSON.parse(storedSearchHistory));

    const storedNavHistory = localStorage.getItem('navigationHistory');
    if (storedNavHistory) {
      const parsedNavHistory = JSON.parse(storedNavHistory);
      setNavigationHistory(parsedNavHistory);
      const currentNavId = workspace.currentChannelId || workspace.currentDirectMessageId;
      const currentNavType = workspace.currentChannelId ? 'channel' : (workspace.currentDirectMessageId ? 'dm' : null);
      if (currentNavId && currentNavType && parsedNavHistory.length > 0) {
        const idx = parsedNavHistory.findIndex((item: NavigationItem) => item.id === currentNavId && item.type === currentNavType);
        setCurrentHistoryIndex(idx > -1 ? idx : parsedNavHistory.length - 1);
      } else if (parsedNavHistory.length > 0) {
        setCurrentHistoryIndex(parsedNavHistory.length - 1);
      }
    }
  }, [workspace]);

  // Effect to refresh data when returning to the tab
  // useEffect(() => {
  //   if (isReturningToVisible && authUser && authProfile) {
  //     // Refresh workspace data when returning to the tab
  //     const refreshData = async () => {
  //       try {
  //         const fetchedWorkspace = await getInitialWorkspaceDataForUser(authUser.id);
  //         if (fetchedWorkspace) {
  //           setWorkspace(prevWorkspace => {
  //             if (!prevWorkspace) return fetchedWorkspace;
  //
  //             // Create a merged workspace similar to the initial load logic
  //             const refreshedWorkspace = {
  //               ...prevWorkspace,
  //               ...fetchedWorkspace
  //             };
  //
  //             // Keep the current state of dynamically loaded content
  //             // This is a simplified version of the merge logic from loadWorkspaceData
  //             refreshedWorkspace.sections = fetchedWorkspace.sections.map(newSection => {
  //               const prevSection = prevWorkspace.sections.find(ps => ps.id === newSection.id);
  //               return {
  //                 ...newSection,
  //                 channels: newSection.channels.map(newChannel => {
  //                   const prevChannel = prevSection?.channels.find(pc => pc.id === newChannel.id);
  //                   if (prevChannel) {
  //                     return {
  //                       ...newChannel,
  //                       messages: prevChannel.messages || [],
  //                       threads: prevChannel.threads || {},
  //                       channelTopics: prevChannel.channelTopics || [],
  //                       files: prevChannel.files || [],
  //                     };
  //                   }
  //                   return newChannel;
  //                 }),
  //               };
  //             });
  //
  //             return refreshedWorkspace;
  //           });
  //         }
  //       } catch (error) {
  //         console.error("Error refreshing workspace data:", error);
  //       } finally {
  //         // Reset loading state when done
  //         resetLoadingState();
  //       }
  //     };
  //
  //     refreshData();
  //   }
  // }, [isReturningToVisible, authUser, authProfile, resetLoadingState]);

  useEffect(() => {
    if (workspace && authUser) {
      const userForEffect = authProfile || workspace.users.find(u => u.id === authUser.id);
      const settingsForEffect = workspace.settings;
      const effectiveSetting = userForEffect?.settings?.autoResetNewMessagesOverride ?? settingsForEffect?.showUnreadBadgeDefault ?? true;
      setAutoResetNewMessagesState(effectiveSetting);
    } else {
      setAutoResetNewMessagesState(true);
    }
  }, [workspace, authUser, authProfile]);


  // Effect to fetch messages when currentChannel changes
  useEffect(() => {
    const channelIdToFetch = workspace?.currentChannelId;
    if (channelIdToFetch && workspace) {
      const fetchMessages = async () => {
        // console.log(`Fetching initial messages for channel: ${channelIdToFetch}`);
        // For initial fetch, cursorId is not needed for getMessagesForChannel
        const fetchedMessages = await getMessagesForChannel(channelIdToFetch, getInitialMessageFetchLimit(), undefined, undefined, undefined);
        if (fetchedMessages === null) {
          toast.error("Failed to load messages. Please check your connection.");
          // Optionally, clear existing messages or show an empty state indicator
          // For now, just toast and leave existing messages (if any) or empty array
          setWorkspace(prevWs => {
            if (!prevWs) return null;
            const newSections = prevWs.sections.map(section => ({
              ...section,
              channels: section.channels.map(ch => {
                if (ch.id === channelIdToFetch) {
                  return {
                    ...ch,
                    messages: ch.messages || [], // Keep existing or ensure it's an array
                    hasMoreOlderMessages: false, // Assume no more if fetch failed
                  };
                }
                return ch;
              })
            }));
            return { ...prevWs, sections: newSections };
          });
          return; // Exit early
        }

        setWorkspace(prevWs => {
          if (!prevWs || !prevWs.id || !channelIdToFetch) return prevWs;

          // fetchedMessages from provider are newest-first. Sort them oldest-first for state.
          const sortedMessages = fetchedMessages.slice().sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

          // Organize messages into regular messages and threads
          const { regularMessages, threads } = organizeMessagesIntoThreads(sortedMessages);

          const numberOfMessagesFetched = regularMessages.length; // Use regularMessages for pagination calculations
          const newHasMoreFlag = numberOfMessagesFetched === getInitialMessageFetchLimit();
          const oldestTs = numberOfMessagesFetched > 0 ? regularMessages[0].timestamp : null;
          const oldestId = numberOfMessagesFetched > 0 ? regularMessages[0].id : null;
          const newestTs = numberOfMessagesFetched > 0 ? regularMessages[numberOfMessagesFetched - 1].timestamp : null;
          const newestId = numberOfMessagesFetched > 0 ? regularMessages[numberOfMessagesFetched - 1].id : null;


          if (newestTs) {
            localStorage.setItem(`last_fetched_ts_${channelIdToFetch}`, newestTs);
            // Also store newestId for delta sync cursor
            if (newestId) localStorage.setItem(`last_fetched_id_${channelIdToFetch}`, newestId);
          }

          const sectionIndex = prevWs.sections.findIndex(s => s.channels.some(c => c.id === channelIdToFetch));
          if (sectionIndex === -1) return prevWs;

          const channelIndex = prevWs.sections[sectionIndex].channels.findIndex(c => c.id === channelIdToFetch);
          if (channelIndex === -1) return prevWs;

          const updatedChannel = {
            ...prevWs.sections[sectionIndex].channels[channelIndex],
            messages: regularMessages, // Use only regular messages (non-thread replies)
            threads: { ...prevWs.sections[sectionIndex].channels[channelIndex].threads, ...threads }, // Merge existing threads with new ones
            oldestFetchedMessageTimestamp: oldestTs,
            oldestFetchedMessageId: oldestId,
            newestFetchedMessageId: newestId, // Store newest ID from this initial fetch
            hasMoreOlderMessages: newHasMoreFlag,
            last_fetched_message_timestamp: newestTs || prevWs.sections[sectionIndex].channels[channelIndex].last_fetched_message_timestamp,
          };

          const updatedChannels = [
            ...prevWs.sections[sectionIndex].channels.slice(0, channelIndex),
            updatedChannel,
            ...prevWs.sections[sectionIndex].channels.slice(channelIndex + 1),
          ];

          const updatedSection = {
            ...prevWs.sections[sectionIndex],
            channels: updatedChannels,
          };

          const updatedSections = [
            ...prevWs.sections.slice(0, sectionIndex),
            updatedSection,
            ...prevWs.sections.slice(sectionIndex + 1),
          ];

          return {
            ...prevWs,
            sections: updatedSections,
          };
        });
      };
      // Check if messages are already loaded or if it's a new channel selection
      const currentChannelData = findChannelById(workspace.sections, channelIdToFetch);
      if (!currentChannelData?.messages?.length || currentChannelData.oldestFetchedMessageTimestamp === undefined) {
        fetchMessages();
      }
    }
  }, [workspace?.currentChannelId, workspace?.id]); // Depend on the ID from workspace state

  // Effect to fetch messages when currentDirectMessage changes
  useEffect(() => {
    const dmIdToFetch = workspace?.currentDirectMessageId;
    if (dmIdToFetch && workspace) {
      const fetchDMMessages = async () => {
        // console.log(`Fetching initial messages for DM: ${dmIdToFetch}`);
        // For initial fetch, cursorId is not needed for getMessagesForDirectMessage
        const fetchedMessages = await getMessagesForDirectMessage(dmIdToFetch, getInitialMessageFetchLimit(), undefined, undefined, undefined);
        if (fetchedMessages === null) {
          toast.error("Failed to load messages. Please check your connection.");
          setWorkspace(prevWs => {
            if (!prevWs) return null;
            const newDms = prevWs.directMessages.map(dm => {
              if (dm.id === dmIdToFetch) {
                return {
                  ...dm,
                  messages: dm.messages || [],
                  hasMoreOlderMessages: false,
                };
              }
              return dm;
            });
            return { ...prevWs, directMessages: newDms };
          });
          return; // Exit early
        }

        setWorkspace(prevWs => {
          if (!prevWs || !prevWs.id || !dmIdToFetch) return prevWs;

          // fetchedMessages from provider are newest-first. Sort them oldest-first for state.
          const sortedMessages = fetchedMessages.slice().sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

          // Organize messages into regular messages and threads
          const { regularMessages, threads } = organizeMessagesIntoThreads(sortedMessages);

          const numberOfMessagesFetched = regularMessages.length; // Use regularMessages for pagination calculations
          const newHasMoreFlag = numberOfMessagesFetched === getInitialMessageFetchLimit();
          const oldestTs = numberOfMessagesFetched > 0 ? regularMessages[0].timestamp : null;
          const oldestId = numberOfMessagesFetched > 0 ? regularMessages[0].id : null;
          const newestTs = numberOfMessagesFetched > 0 ? regularMessages[numberOfMessagesFetched - 1].timestamp : null;
          const newestId = numberOfMessagesFetched > 0 ? regularMessages[numberOfMessagesFetched - 1].id : null;

          if (newestTs) {
            localStorage.setItem(`last_fetched_ts_${dmIdToFetch}`, newestTs);
            // Also store newestId for delta sync cursor
            if (newestId) localStorage.setItem(`last_fetched_id_${dmIdToFetch}`, newestId);
          }

          const dmIndex = prevWs.directMessages.findIndex(d => d.id === dmIdToFetch);
          if (dmIndex === -1) return prevWs;

          const updatedDm = {
            ...prevWs.directMessages[dmIndex],
            messages: regularMessages, // Use only regular messages (non-thread replies)
            threads: { ...prevWs.directMessages[dmIndex].threads, ...threads }, // Merge existing threads with new ones
            oldestFetchedMessageTimestamp: oldestTs,
            oldestFetchedMessageId: oldestId,
            newestFetchedMessageId: newestId, // Store newest ID from this initial fetch
            hasMoreOlderMessages: newHasMoreFlag,
            last_fetched_message_timestamp: newestTs || prevWs.directMessages[dmIndex].last_fetched_message_timestamp,
          };

          const updatedDirectMessages = [
            ...prevWs.directMessages.slice(0, dmIndex),
            updatedDm,
            ...prevWs.directMessages.slice(dmIndex + 1),
          ];

          return {
            ...prevWs,
            directMessages: updatedDirectMessages,
          };
        });
      };
      const currentDmData = workspace.directMessages.find(dm => dm.id === dmIdToFetch);
      if (!currentDmData?.messages?.length || currentDmData.oldestFetchedMessageTimestamp === undefined) {
        fetchDMMessages();
      }
    }
  }, [workspace?.currentDirectMessageId, workspace?.id]);

  // Effect for Realtime message updates
  useEffect(() => {
    if (!authUser || !workspace?.id) {
      return;
    }

    const messagesChannel = supabase
      .channel(`realtime:messages:${workspace.id}`) // Unique channel name per workspace
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          // We could filter by workspace_id if messages table had it.
          // For now, we'll receive all messages and filter client-side.
        },
        (payload) => {
          // console.log('Realtime: New message received', payload);
          const newMessageFromDb = payload.new as any; // Cast to any to handle snake_case

          // Transform to client-side Message type
          const newMessage = transformSupabaseMessage(newMessageFromDb);

          setWorkspace(prevWs => {
            if (!prevWs || !authUser) return prevWs;

            // Store a copy of unread counts before applying the new message
            const unreadCountsBefore: Record<string, { count: number | undefined, type: 'channel' | 'dm' }> = {};
            prevWs.sections.forEach(s => s.channels.forEach(c => unreadCountsBefore[c.id] = { count: c.unreadCount, type: 'channel' }));
            prevWs.directMessages.forEach(dm => unreadCountsBefore[dm.id] = { count: dm.unreadCount, type: 'dm' });

            const updatedWs = applyRealtimeMessageToWorkspace(prevWs, newMessage, authUser.id);

            // START: Added for Delta Sync localStorage update from Realtime
            if (newMessage.timestamp) {
              // Use newMessageFromDb (raw payload) to get channel_id or dm_id
              const conversationId = newMessageFromDb.channel_id || newMessageFromDb.dm_id;
              if (conversationId) {
                // console.log(`[Realtime] Updating localStorage last_fetched_ts_${conversationId} to ${newMessage.timestamp}`);
                localStorage.setItem(`last_fetched_ts_${conversationId}`, newMessage.timestamp);
              }
            }
            // END: Added for Delta Sync localStorage update from Realtime

            // After applying, check for changes in unread counts for non-active conversations
            if (newMessage.userId !== authUser.id) { // Only consider messages from others
              const activeChannelId = updatedWs.currentChannelId;
              const activeDmId = updatedWs.currentDirectMessageId;

              Object.keys(unreadCountsBefore).forEach(convoId => {
                const oldInfo = unreadCountsBefore[convoId];
                let newUnread: number | undefined;
                let isChannel = false;

                if (oldInfo.type === 'channel') {
                  const ch = updatedWs.sections.flatMap(s => s.channels).find(c => c.id === convoId);
                  if (ch) {
                    newUnread = ch.unreadCount;
                    isChannel = true;
                  }
                } else { // DM
                  const dm = updatedWs.directMessages.find(d => d.id === convoId);
                  if (dm) newUnread = dm.unreadCount;
                }

                const oldUnreadCount = oldInfo.count || 0;
                const currentUnreadCount = newUnread || 0;

                if (currentUnreadCount > oldUnreadCount) {
                  const isActiveConversation = isChannel ? convoId === activeChannelId : convoId === activeDmId;
                  if (!isActiveConversation) {
                    setPersistentUnreadInfo(prevPui => ({
                      ...prevPui,
                      [convoId]: {
                        // Preserve existing lastReadMessageTimestamp, only update unreadCount
                        lastReadMessageTimestamp: prevPui[convoId]?.lastReadMessageTimestamp,
                        unreadCount: currentUnreadCount,
                      }
                    }));
                    // console.log(`[Persistent Unread] RT ${oldInfo.type} ${convoId} unread updated to ${currentUnreadCount}`);
                  }
                }
              });
            }
            return updatedWs;
          });
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          // console.log(`Realtime: Subscribed to messages in workspace ${workspace.id}`);
        }
        if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT' || status === 'CLOSED') {
          console.error(`Realtime: Subscription error for workspace ${workspace.id}`, status, err);
          // Check workspace setting before showing toast
          console.log('[Realtime Toast Debug] suppressRealtimeConnectionToast setting:', workspace?.settings?.suppressRealtimeConnectionToast);
          console.log('[Realtime Toast Debug] workspace.settings:', workspace?.settings);
          // Only show toast if suppressRealtimeConnectionToast is false or undefined (default behavior)
          if (!workspace?.settings?.suppressRealtimeConnectionToast) {
            console.log('[Realtime Toast Debug] Showing toast because setting is false/undefined');
            toast.warning("Realtime connection issue. Some updates may be delayed. Attempting to reconnect...");
          } else {
            console.log('[Realtime Toast Debug] Suppressing toast because setting is true');
          }
          // Optionally, implement more sophisticated retry logic or user notification here
        }
      });

    // Cleanup subscription on component unmount or when dependencies change
    return () => {
      // console.log(`Realtime: Unsubscribing from messages in workspace ${workspace.id}`);
      supabase.removeChannel(messagesChannel);
    };
  }, [authUser, workspace?.id]); // Re-subscribe if user or workspace ID changes

  const fetchNewerMessagesForConversation = useCallback(async (conversationId: string, conversationType: 'channel' | 'dm') => {
    if (!workspace || !authUser) {
      // console.log("[DeltaSync] Skipping fetch: no workspace or authUser");
      return;
    }

    let conversation: Channel | DirectMessage | undefined;
    let lastFetchedTimestampFromState: string | null | undefined;
    let lastFetchedIdFromState: string | null | undefined;

    if (conversationType === 'channel') {
      conversation = findChannelById(workspace.sections, conversationId);
      lastFetchedTimestampFromState = conversation?.last_fetched_message_timestamp;
      lastFetchedIdFromState = conversation?.newestFetchedMessageId;
    } else {
      conversation = workspace.directMessages.find(dm => dm.id === conversationId);
      lastFetchedTimestampFromState = conversation?.last_fetched_message_timestamp;
      lastFetchedIdFromState = conversation?.newestFetchedMessageId;
    }

    // Prioritize localStorage timestamp and ID for delta sync
    const timestampFromLocalStorage = localStorage.getItem(`last_fetched_ts_${conversationId}`);
    const idFromLocalStorage = localStorage.getItem(`last_fetched_id_${conversationId}`);

    const actualSinceTimestampUsed = timestampFromLocalStorage || lastFetchedTimestampFromState;
    const actualSinceIdUsed = idFromLocalStorage || lastFetchedIdFromState;

    if (!conversation || !actualSinceTimestampUsed || !actualSinceIdUsed) {
      return;
    }

    try {
      let allNewerMessages: Message[] = [];
      let hasMoreMessages = true;
      let fetchCount = 0;
      const maxFetches = 10; // Safety limit to prevent infinite loops
      let currentSinceTimestamp = actualSinceTimestampUsed;
      let currentSinceId = actualSinceIdUsed;

      // Continue fetching until we get all new messages or hit safety limit
      while (hasMoreMessages && fetchCount < maxFetches) {
        fetchCount++;

        let newerMessages: Message[] | null = null;
        if (conversationType === 'channel') {
          newerMessages = await getMessagesForChannel(conversationId, getDeltaMessageFetchLimit(), undefined, currentSinceId, currentSinceTimestamp);
        } else {
          newerMessages = await getMessagesForDirectMessage(conversationId, getDeltaMessageFetchLimit(), undefined, currentSinceId, currentSinceTimestamp);
        }

        if (newerMessages === null) {
          toast.error("Failed to fetch newer messages. Please check your connection.");
          return;
        }

        if (newerMessages.length === 0) {
          // No more new messages
          hasMoreMessages = false;
          break;
        }

        // Add to our collection
        allNewerMessages.push(...newerMessages);

        // Check if we got a full batch (indicating there might be more)
        if (newerMessages.length < getDeltaMessageFetchLimit()) {
          // Got fewer messages than requested, so we've reached the end
          hasMoreMessages = false;
        } else {
          // Update cursors for next iteration
          const lastMessage = newerMessages[newerMessages.length - 1];
          currentSinceTimestamp = lastMessage.timestamp;
          currentSinceId = lastMessage.id;
        }
      }

      if (fetchCount >= maxFetches) {
        console.warn(`[DeltaSync] Hit maximum fetch limit (${maxFetches}) for ${conversationType} ${conversationId}. There may be more messages.`);
        toast.warning("Large number of new messages detected. Some messages may not be loaded. Please refresh the channel.");
      }

      // Log the delta sync results for debugging
      if (allNewerMessages.length > 0) {
        console.log(`[DeltaSync] Fetched ${allNewerMessages.length} new messages in ${fetchCount} batch(es) for ${conversationType} ${conversationId}`);
      }

      // newerMessages from provider (when using sinceTimestamp & cursorId) are OLDEST-FIRST.
      // No need to reverse them here as they are already in the correct order for appending.
      if (allNewerMessages.length > 0) {
        const newLastFetchedTimestampForDeltaSync = allNewerMessages[allNewerMessages.length - 1].timestamp;
        const newLastFetchedIdForDeltaSync = allNewerMessages[allNewerMessages.length - 1].id;


        setWorkspace(prevWs => {
          if (!prevWs) return null;

          let existingMessages: Message[] = [];
          if (conversationType === 'channel') {
            const currentCh = findChannelById(prevWs.sections, conversationId);
            existingMessages = currentCh?.messages || []; // These are oldest-first in state
          } else {
            const currentDm = prevWs.directMessages.find(d => d.id === conversationId);
            existingMessages = currentDm?.messages || []; // These are oldest-first in state
          }
          // `allNewerMessages` (from provider, oldest-first) are the newly fetched messages.
          // `existingMessages` (from state, oldest-first).

          // 1. Combine arrays
          const combinedMessages = [...existingMessages, ...allNewerMessages];

          // 2. Deduplicate by `id`, keeping the latest instance (from `newerMessages` if IDs collide)
          const messageMap = new Map<string, Message>();
          combinedMessages.forEach(msg => messageMap.set(msg.id, msg));
          const uniqueMessages = Array.from(messageMap.values());

          // 3. Sort uniqueMessages chronologically by `timestamp` (ascending, oldest first)
          const uniqueSortedMessages = uniqueMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

          // The overall last message timestamp for the conversation should be the newest in the merged list
          const newOverallLastMessageTimestamp = uniqueSortedMessages.length > 0
            ? uniqueSortedMessages[uniqueSortedMessages.length - 1].timestamp
            : (conversation?.lastMessageTimestamp || newLastFetchedTimestampForDeltaSync);
          localStorage.setItem(`last_fetched_ts_${conversationId}`, newLastFetchedTimestampForDeltaSync);
          localStorage.setItem(`last_fetched_id_${conversationId}`, newLastFetchedIdForDeltaSync);


          if (conversationType === 'channel') {
            const newSections = prevWs.sections.map(section => ({
              ...section,
              channels: section.channels.map(ch => {
                if (ch.id === conversationId) {
                  return {
                    ...ch,
                    messages: uniqueSortedMessages,
                    last_fetched_message_timestamp: newLastFetchedTimestampForDeltaSync,
                    newestFetchedMessageId: newLastFetchedIdForDeltaSync, // Update newest ID
                    lastMessageTimestamp: newOverallLastMessageTimestamp,
                  };
                }
                return ch;
              })
            }));
            return { ...prevWs, sections: newSections };
          } else { // Direct Message
            const newDms = prevWs.directMessages.map(dm => {
              if (dm.id === conversationId) {
                return {
                  ...dm,
                  messages: uniqueSortedMessages,
                    last_fetched_message_timestamp: newLastFetchedTimestampForDeltaSync,
                    newestFetchedMessageId: newLastFetchedIdForDeltaSync, // Update newest ID
                    lastMessageTimestamp: newOverallLastMessageTimestamp,
                };
              }
              return dm;
            });
            return { ...prevWs, directMessages: newDms };
          }
        });
      }
    } catch (error) {
      console.error(`Error fetching newer messages for ${conversationType} ${conversationId}:`, error);
      toast.error("An error occurred while fetching new messages.");
    }
  }, [workspace, authUser, setWorkspace]);

  // Derive activeConversationObject outside the effect for more precise dependency tracking
  const activeConversationObject = workspace?.currentChannelId
    ? findChannelById(workspace.sections, workspace.currentChannelId)
    : workspace?.currentDirectMessageId
    ? workspace.directMessages.find(dm => dm.id === workspace.currentDirectMessageId)
    : null;

  // Derive the specific timestamp as a primitive value for more precise dependency tracking
  const specificActiveTimestamp = activeConversationObject?.last_fetched_message_timestamp || null;

  // Effect to trigger delta sync on tab visibility change
  // This must be AFTER fetchNewerMessagesForConversation is defined.
  useEffect(() => {
    const wasPreviouslyHidden = prevVisibilityStateRef.current !== 'visible';
    const isNowVisible = document.visibilityState === 'visible';

    if (isNowVisible && wasPreviouslyHidden) {
      if (authUser && workspace) {
        const activeConversationId = workspace.currentChannelId || workspace.currentDirectMessageId;
        let conversationType: 'channel' | 'dm' | null = null;

        if (workspace.currentChannelId) {
          conversationType = 'channel';
        } else if (workspace.currentDirectMessageId) {
          conversationType = 'dm';
        }

        if (activeConversationId && conversationType) {
          const timestampFromLocalStorage = localStorage.getItem(`last_fetched_ts_${activeConversationId}`);
          const idFromLocalStorage = localStorage.getItem(`last_fetched_id_${activeConversationId}`);
          if (timestampFromLocalStorage && idFromLocalStorage) {
            fetchNewerMessagesForConversation(activeConversationId, conversationType)
              .catch(error => console.error(`[DeltaSync Visibility] Error during visibility change fetch for ${conversationType} ${activeConversationId}:`, error));
          }
        }
      }
    }
    prevVisibilityStateRef.current = document.visibilityState;
  }, [
    document.visibilityState,
    authUser,
    workspace, // For activeConversationId, conversationType, and the conditional check
    fetchNewerMessagesForConversation,
  ]);

  // Show loading screen when:
  // 1. Initial auth or workspace loading - use skeleton for first load
  if (authLoading || workspaceLoading || !workspace || !authUser) {
    return <AppLoadingScreen variant="skeleton" />;
  }

  const updateNavigationHistory = (newHistory: NavigationItem[], newIndex: number) => {
    setNavigationHistory(newHistory);
    setCurrentHistoryIndex(newIndex);
    localStorage.setItem('navigationHistory', JSON.stringify(newHistory));
  };

  const addToNavigationHistory = (item: NavigationItem) => {
    if (navigationHistory[currentHistoryIndex]?.id === item.id && navigationHistory[currentHistoryIndex]?.type === item.type) return;
    const newHistory = navigationHistory.slice(0, currentHistoryIndex + 1);
    newHistory.push(item);
    const limitedHistory = newHistory.slice(-50);
    updateNavigationHistory(limitedHistory, limitedHistory.length - 1);
  };

  const getCurrentUser = (): User => {
    if (authProfile) return { ...authProfile, id: authUser.id } as User;
    const wsUser = workspace.users.find(user => user.id === authUser.id);
    if (wsUser) return wsUser;
    return { id: authUser.id, name: authUser.email || "User", avatar: authUser.user_metadata?.avatar_url || "" } as User;
  };

  // Use utility functions for finding section and channel
  const currentSection = findSectionById(workspace.sections, workspace.currentSectionId);
  const currentChannel = findChannelById(workspace.sections, workspace.currentChannelId);
  const currentDirectMessage = workspace.currentDirectMessageId ? workspace.directMessages.find(dm => dm.id === workspace.currentDirectMessageId) || null : null;

  // Use utility function for finding/creating thread
  const currentThread = workspace.activeThreadId ? findOrCreateThreadFromContext(workspace.activeThreadId, currentChannel || currentDirectMessage) : null;
  const selectedMember = selectedMemberId && workspace ? workspace.users.find(user => user.id === selectedMemberId) || null : null;

  const setCurrentSection = (sectionId: string | null) => {
    setWorkspace(prev => prev ? applySetCurrentSectionToWorkspace(prev, sectionId) : null);
  };

  const setCurrentChannel = (channelId: string | null, fromNavigation = false, fromTopicNavigation = false) => {
    if (!workspace) return;
    if (channelId === workspace.currentChannelId && !fromNavigation && !fromTopicNavigation) return;

    let sectionIdForChannel = workspace.currentSectionId;
    if (channelId) {
      const foundSection = workspace.sections.find(s => s.channels.some(ch => ch.id === channelId));
      if (foundSection) sectionIdForChannel = foundSection.id;
    } else {
      sectionIdForChannel = null; // Clear section if channel is cleared
    }

    setWorkspace(prev => prev ? applySetCurrentChannelToWorkspace(prev, channelId, sectionIdForChannel) : null);

    if (channelId && !fromNavigation && !fromTopicNavigation) addToNavigationHistory({ type: 'channel', id: channelId });
    if (isSearchViewActive && !fromNavigation && !fromTopicNavigation) setIsSearchViewActive(false);
    if (!fromTopicNavigation) setCurrentChannelActiveView(null);
  };

  const setCurrentDirectMessage = (directMessageId: string | null, fromNavigation = false) => {
    // Store the current DM ID for comparison (if active conversation is changing)
    const isChangingConversation = workspace.currentDirectMessageId !== directMessageId;

    // First update the selection state without resetting unread count
    setWorkspace(prev => prev ? applySetCurrentDirectMessageToWorkspace(prev, directMessageId) : null);

    // If we're changing conversations and it's not from a navigation action,
    // add the new DM to navigation history
    if (directMessageId && !fromNavigation) {
      addToNavigationHistory({ type: 'dm', id: directMessageId });
    }

    // Reset search view if needed
    if (isSearchViewActive && !fromNavigation) {
      setIsSearchViewActive(false);
    }

    // Reset channel view
    setCurrentChannelActiveView(null);

    // If we're changing conversations, set a longer delay before resetting the unread count
    // This allows the unread badge to be displayed for a sufficient time before being reset
    if (isChangingConversation && directMessageId) {
      // Increased timeout from 2 seconds to 5 seconds to make the badge more visible
      setTimeout(() => {
        // console.log(`[Debug DM Unread] Resetting unread count for DM ID: ${directMessageId} after timeout`);
        // Only reset the unread count if this DM is still the current one
        setWorkspace(prev => {
          if (!prev || prev.currentDirectMessageId !== directMessageId) return prev;

          // Log current unread count before resetting
          const dmBeforeReset = prev.directMessages.find(dm => dm.id === directMessageId);
          if (dmBeforeReset && dmBeforeReset.unreadCount) {
            // console.log(`[Debug DM Unread] Resetting unread count for DM ID: ${directMessageId} from ${dmBeforeReset.unreadCount} to 0`);
          }

          return {
            ...prev,
            directMessages: prev.directMessages.map(dm =>
              dm.id === directMessageId ? { ...dm, unreadCount: 0 } : dm
            )
          };
        });
      }, 5000); // Increased from 2000 to 5000 milliseconds
    }
  };

  const setActiveThread = (messageId: string | null) => {
    setWorkspace(prev => {
      if (!prev) return null;
      const activeContext = prev.currentChannelId
        ? findChannelById(prev.sections, prev.currentChannelId)
        : (prev.currentDirectMessageId ? prev.directMessages.find(dm => dm.id === prev.currentDirectMessageId) : null);
      return applySetActiveThreadToWorkspace(prev, messageId, activeContext);
    });
  };

  const sendMessage = async (content: string, channelIdParam?: string, directMessageId?: string, threadId?: string, topicIdParam?: string, attachmentsData?: LocalAttachment[]) => {
    if ((!content.trim() && (!attachmentsData || attachmentsData.length === 0)) || !workspace || !authUser) return;

    // Log attachments if present (for Phase 1 debugging)
    if (attachmentsData && attachmentsData.length > 0) {
      console.log('[sendMessage] Attachments to send (Phase 1):', attachmentsData);
      // In Phase 2, this data will be structured differently (e.g., array of file IDs)
      // or the `attachmentsData` will be processed here to upload files if not already uploaded.
    }

    const messageToInsert = {
      content,
      user_id: authUser.id,
      channel_id: channelIdParam || (directMessageId ? null : currentChannel?.id),
      dm_id: directMessageId || (channelIdParam ? null : currentDirectMessage?.id),
      parent_message_id: threadId,
      topic_id: topicIdParam,
      // Supabase will add id and timestamp
    };

    // Optimistic update
    const clientTempId = `temp-${Date.now()}`;
    const optimisticMessage: Message = {
      id: clientTempId,
      content,
      timestamp: new Date().toISOString(),
      userId: authUser.id,
      threadId,
      channelId: messageToInsert.channel_id || undefined,
      topicId: topicIdParam,
      files: attachmentsData ? attachmentsData.map(att => ({ // Temporary client-side representation for optimistic update
        id: att.id,
        name: att.name,
        type: att.type,
        url: att.dataUrl || (att.textContent ? `text_content_placeholder_${att.id}` : `file_placeholder_${att.id}`), // Placeholder URL
        size_bytes: att.size,
        uploaded_by_user_id: authUser.id,
        created_at: new Date().toISOString(),
      })) : undefined,
    };

    setWorkspace(prev => {
      if (!prev) return null;
      return applyOptimisticMessageUpdate(prev, optimisticMessage, messageToInsert.channel_id, messageToInsert.dm_id, threadId);
    });

    try {
      const pAttachmentsData = attachmentsData?.map(att => {
        // Prepare the attachment data for the RPC
        // In Phase 1/2, 'url' temporarily holds base64 or text content.
        // fileObject is not sent to this RPC.
        return {
          name: att.name,
          type: att.type,
          size: att.size,
          url: att.dataUrl || att.textContent || null, // Prioritize dataUrl, then textContent
          // uploaded_by_user_id will be set by the RPC using auth.uid()
          // message_id and channel_id will be set by the RPC
        };
      });

      const rpcParams = {
        p_content: content,
        p_workspace_id: workspace.id,
        p_channel_id: messageToInsert.channel_id || null,
        p_dm_id: messageToInsert.dm_id || null,
        p_parent_message_id: threadId || null,
        p_topic_id: messageToInsert.topic_id || null,
        p_attachments_data: pAttachmentsData && pAttachmentsData.length > 0 ? pAttachmentsData : null, // Pass null if no attachments
      };

      const { data: dbMessage, error } = await supabase.rpc('send_message', rpcParams);

      if (error) {
        console.error("Error sending message via RPC:", error);
        // Revert optimistic update
        setWorkspace(prev => {
          if (!prev) return null;
          return revertOptimisticMessageUpdate(prev, clientTempId, messageToInsert.channel_id, messageToInsert.dm_id, threadId);
        });
      } else if (dbMessage) { // dbMessage is the direct object returned by RPC
        // If successful, update the optimistic message with the real ID from the database.
        setWorkspace(prev => {
          if (!prev) return null;
          const updatedWs = JSON.parse(JSON.stringify(prev));
          const targetChannelIdSuccess = messageToInsert.channel_id;
          const targetDMIdSuccess = messageToInsert.dm_id;

          // Transform the dbMessage (which is raw from RPC) to a client-side Message type
          // The RPC returns the newly inserted row, which might have snake_case keys.
          // transformSupabaseMessage handles this.
          const finalMessageFromDb = transformSupabaseMessage(dbMessage);

          if (threadId) {
            if (targetChannelIdSuccess) {
              const ch = updatedWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === targetChannelIdSuccess);
              if (ch && ch.threads[threadId]) {
                ch.threads[threadId].messages = updateOptimisticMessageInArray(ch.threads[threadId].messages, clientTempId, finalMessageFromDb);
              }
            } else if (targetDMIdSuccess) {
              const dm = updatedWs.directMessages.find((d: DirectMessage) => d.id === targetDMIdSuccess);
              if (dm && dm.threads[threadId]) {
                dm.threads[threadId].messages = updateOptimisticMessageInArray(dm.threads[threadId].messages, clientTempId, finalMessageFromDb);
              }
            }
          } else if (targetChannelIdSuccess) {
            const ch = updatedWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === targetChannelIdSuccess);
            if (ch) {
              ch.messages = updateOptimisticMessageInArray(ch.messages, clientTempId, finalMessageFromDb);
            }
          } else if (targetDMIdSuccess) {
            const dm = updatedWs.directMessages.find((d: DirectMessage) => d.id === targetDMIdSuccess);
            if (dm) {
              dm.messages = updateOptimisticMessageInArray(dm.messages, clientTempId, finalMessageFromDb);
            }
          }
          return updatedWs;
        });
      }
    } catch (e) {
      console.error("Exception sending message:", e);
      // Consider if additional revert logic is needed here, though the error block above should handle most cases.
    }
  };

  const toggleSidebar = () => setIsSidebarOpen(prev => !prev);

  const addReaction = async (messageId: string, emoji: string) => {
    if (!authUser) {
      toast.error("User not authenticated. Cannot add reaction.");
      return;
    }

    // Optimistic update
    setWorkspace(prev => {
      if (!prev) return null; // authUser is already checked
      return applyReactionUpdateToWorkspace(prev, messageId, emoji, authUser.id);
    });

    try {
      const { data: rpcResult, error: rpcError } = await supabase.rpc('toggle_reaction', {
        p_message_id: messageId,
        p_emoji: emoji,
      });

      if (rpcError) {
        throw rpcError;
      }
      // console.log(`Reaction '${emoji}' on message ${messageId} toggled on server. New state (true=exists):`, rpcResult);
      // Optionally, verify rpcResult against the optimistic local state and correct if necessary,
      // but for a toggle, the optimistic update should align if the initial state was correct.
    } catch (error: any) {
      console.error(`Failed to toggle reaction '${emoji}' on message ${messageId} on server:`, error);
      toast.error(`Failed to update reaction: ${error.message || 'Unknown error'}`);

      // Revert optimistic update
      setWorkspace(prev => {
        if (!prev) return null; // authUser is already checked
        // Calling it again toggles it back
        return applyReactionUpdateToWorkspace(prev, messageId, emoji, authUser.id);
      });
    }
  };

  const addChannel = async (name: string, sectionId: string, isPrivate: boolean = false) => {
    if (!name.trim() || !sectionId || !workspace || !authUser) return;

    try {
      // 1. Insert the new channel into Supabase
      const { data: newDbChannel, error: channelError } = await supabase
        .from('channels')
        .insert({
          name: name.trim(),
          section_id: sectionId,
          workspace_id: workspace.id,
          is_private: isPrivate,
        })
        .select()
        .single();

      if (channelError) {
        handleSupabaseError(channelError, OPERATION_CONTEXTS.CREATE_CHANNEL, {
          operation: 'channel creation',
          fallbackMessage: 'Failed to create channel'
        });
        return;
      }

      if (!newDbChannel) {
        console.error('No data returned after creating channel.');
        toast.error('Failed to create channel: No data returned from server.');
        return;
      }

      // 2. Add the current user as a member of the new channel
      const { error: memberError } = await supabase
        .from('channel_members')
        .insert({
          channel_id: newDbChannel.id,
          user_id: authUser.id,
        });

      if (memberError) {
        handleSupabaseError(memberError, OPERATION_CONTEXTS.CREATE_CHANNEL, {
          operation: 'channel membership setup',
          fallbackMessage: 'Failed to set up channel members'
        });
        // Note: Channel is created but user isn't a member. This state might need more robust handling (e.g., delete the channel).
        // For now, we'll proceed with adding channel to UI but user might not be able to see/use it.
        // Or, simply return and don't add to UI if membership is critical.
        // Let's return for now to avoid inconsistent state in UI.
        return;
      }

      // 3. Construct the client-side Channel object
      const newChannelForState: Channel = {
        id: newDbChannel.id,
        displayId: generateDisplayId(newDbChannel.id, 'c-'),
        name: newDbChannel.name,
        description: newDbChannel.description,
        isPrivate: newDbChannel.is_private,
        members: [authUser.id], // Start with the creator
        messages: [],
        threads: {},
        createdAt: newDbChannel.created_at,
        channelTopics: [],
        files: [],
        // Initialize other optional fields as needed
      };

      // 4. Update the local workspace state
      setWorkspace(prev => {
        if (!prev) return null;
        return applyAddChannelUpdateToWorkspace(prev, newChannelForState, sectionId);
      });
      toast.success(`Channel "${newChannelForState.name}" created!`);

    } catch (error: any) {
      handleUnexpectedError(error, 'channel creation');
    }
  };

  const addSection = async (name: string): Promise<Section | null> => {
    if (!name.trim() || !workspace || !authUser) {
      toast.error("Cannot add section: Invalid input or not authenticated.");
      return null;
    }

    try {
      // Determine the next display_order
      let nextDisplayOrder = 0;
      if (workspace.sections && workspace.sections.length > 0) {
        const maxOrder = Math.max(...workspace.sections.map(s => s.display_order || 0));
        nextDisplayOrder = maxOrder + 1;
      }

      const { data: newDbSection, error } = await supabase
        .from('sections')
        .insert({
          name: name.trim(),
          workspace_id: workspace.id,
          display_order: nextDisplayOrder,
        })
        .select()
        .single();

      if (error) {
        handleSupabaseError(error, OPERATION_CONTEXTS.CREATE_SECTION, {
          operation: 'section creation',
          fallbackMessage: 'Failed to create section'
        });
        return null;
      }

      if (!newDbSection) {
        toast.error('Failed to create section: No data returned from server.');
        return null;
      }

      const newSectionForState: Section = {
        id: newDbSection.id,
        displayId: generateDisplayId(newDbSection.id, 's-'),
        name: newDbSection.name,
        channels: [],
        workspace_id: newDbSection.workspace_id,
        display_order: newDbSection.display_order,
      };

      setWorkspace(prev => prev ? applyAddSectionUpdateToWorkspace(prev, newSectionForState) : null);
      toast.success(`Section "${newSectionForState.name}" created!`);
      return newSectionForState;

    } catch (e: any) {
      handleUnexpectedError(e, 'section creation');
      return null;
    }
  };

  const updateSection = async (sectionId: string, updates: Partial<Pick<Section, 'name' | 'display_order'>>): Promise<Section | null> => {
    if (!workspace || !authUser) {
      toast.error("Cannot update section: Not authenticated or no workspace.");
      return null;
    }
    if (Object.keys(updates).length === 0) {
      toast.info("No changes provided for section update.");
      return findSectionById(workspace.sections, sectionId);
    }

    try {
      const { data: updatedDbSection, error } = await supabase
        .from('sections')
        .update(updates)
        .eq('id', sectionId)
        .select()
        .single();

      if (error) {
        handleSupabaseError(error, OPERATION_CONTEXTS.UPDATE_SECTION, {
          operation: 'section update',
          fallbackMessage: 'Failed to update section'
        });
        return null;
      }

      if (!updatedDbSection) {
        toast.error('Failed to update section: No data returned from server.');
        return null;
      }

      const updatedSectionForState: Section = {
        id: updatedDbSection.id,
        displayId: generateDisplayId(updatedDbSection.id, 's-'),
        name: updatedDbSection.name,
        channels: findSectionById(workspace.sections, sectionId)?.channels || [], // Preserve existing channels
        workspace_id: updatedDbSection.workspace_id,
        display_order: updatedDbSection.display_order,
      };

      setWorkspace(prev => {
        if (!prev) return null;
        const newSections = prev.sections.map(s =>
          s.id === sectionId ? { ...s, ...updatedSectionForState } : s
        );
        // If display_order changed, sort sections
        if (updates.display_order !== undefined) {
          newSections.sort((a, b) => (a.display_order || 0) - (b.display_order || 0));
        }
        return { ...prev, sections: newSections };
      });

      toast.success(`Section "${updatedSectionForState.name}" updated!`);
      return updatedSectionForState;

    } catch (e: any) {
      handleUnexpectedError(e, 'section update');
      return null;
    }
  };

  const deleteSection = async (sectionId: string): Promise<boolean> => {
    if (!workspace || !authUser) {
      toast.error("Cannot delete section: Not authenticated or no workspace.");
      return false;
    }

    // Basic check: prevent deleting section if it contains channels.
    // More complex logic (e.g., moving channels) is out of scope for this task.
    const sectionToDelete = findSectionById(workspace.sections, sectionId);
    if (sectionToDelete && sectionToDelete.channels.length > 0) {
      toast.error("Cannot delete section: Section is not empty. Please move or delete channels first.");
      return false;
    }

    try {
      const { error } = await supabase
        .from('sections')
        .delete()
        .eq('id', sectionId);

      if (error) {
        handleSupabaseError(error, OPERATION_CONTEXTS.DELETE_SECTION, {
          operation: 'section deletion',
          fallbackMessage: 'Failed to delete section'
        });
        return false;
      }

      setWorkspace(prev => {
        if (!prev) return null;
        return {
          ...prev,
          sections: prev.sections.filter(s => s.id !== sectionId),
          // If the deleted section was the current one, clear currentSectionId
          currentSectionId: prev.currentSectionId === sectionId ? null : prev.currentSectionId,
        };
      });

      toast.success(`Section deleted successfully!`);
      return true;

    } catch (e: any) {
      handleUnexpectedError(e, 'section deletion');
      return false;
    }
  };

  const addDirectMessage = async (userId: string): Promise<string | null> => {
    if (!workspace || !authUser) {
      toast.error("Cannot start direct message: Not authenticated or workspace not loaded.");
      return null;
    }

    // Prevent creating a DM with oneself
    if (userId === authUser.id) {
      toast.info("You cannot start a direct message with yourself.");
      // const selfDm = workspace.directMessages.find(dm => dm.participants.length === 1 && dm.participants[0] === authUser.id);
      // if (selfDm) {
      //   setCurrentDirectMessage(selfDm.id);
      //   return selfDm.id;
      // }
      return null;
    }

    const existingClientDm = workspace.directMessages.find(dm =>
      dm.participants.length === 2 &&
      dm.participants.includes(userId) &&
      dm.participants.includes(authUser.id)
    );

    if (existingClientDm && workspace.currentDirectMessageId === existingClientDm.id) {
      // console.log(`Already viewing DM with ${userId} (ID: ${existingClientDm.id}). No action needed.`);
      return existingClientDm.id;
    }
    if (existingClientDm) {
      // console.log(`Client-side DM with ${userId} exists (ID: ${existingClientDm.id}). Setting as current.`);
      setCurrentDirectMessage(existingClientDm.id); // This handles navigation history
      return existingClientDm.id;
    }

    try {
      // console.log(`Calling create_direct_message_session RPC for target_user_id: ${userId}`);
      const { data: sessionData, error: rpcError } = await supabase.rpc('create_direct_message_session', {
        p_target_user_id: userId
      });

      if (rpcError) {
        console.error('Error calling create_direct_message_session RPC:', rpcError);
        handleSupabaseError(rpcError, OPERATION_CONTEXTS.CREATE_DM_SESSION, {
          operation: 'direct message session creation',
          fallbackMessage: 'Failed to start direct message. Please try again.'
        });
        return null;
      }

      if (!sessionData || !sessionData.id) {
        console.error('Invalid data returned from create_direct_message_session RPC:', sessionData);
        toast.error('Failed to start direct message: Server returned invalid data.');
        return null;
      }

      const { id: sessionId, created_at: sessionCreatedAt, participants: sessionParticipants, is_new: isNewSession } = sessionData;

      if (isNewSession) {
        // console.log(`New DM session created (ID: ${sessionId}). Adding to client state.`);
        const newDmForState: DirectMessage = {
          id: sessionId,
          displayId: generateDisplayId(sessionId, 'dm-'),
          participants: sessionParticipants,
          messages: [],
          threads: {},
          createdAt: sessionCreatedAt,
          unreadCount: 0,
          isUnreadCountPlaceholder: false,
          oldestFetchedMessageTimestamp: null,
          hasMoreOlderMessages: false,
          last_fetched_message_timestamp: null,
          oldestFetchedMessageId: null,
          newestFetchedMessageId: null,
        };

        setWorkspace(prev => {
          if (!prev) return null;
          const updatedWs = applyAddDirectMessageUpdateToWorkspace(prev, newDmForState);
          return applySetCurrentDirectMessageToWorkspace(updatedWs, newDmForState.id); // This sets current and handles nav history
        });
        return newDmForState.id;

      } else { // Existing session found via RPC
        // console.log(`Existing DM session found (ID: ${sessionId}). Setting as current.`);
        const existingDmInState = workspace.directMessages.find(dm => dm.id === sessionId);
        if (existingDmInState) {
          setCurrentDirectMessage(sessionId); // This sets current and handles nav history
          return sessionId;
        } else {
          console.warn(`Existing DM session (ID: ${sessionId}) from RPC not found in client state. Adding it now.`);
          const recoveredDm: DirectMessage = {
            id: sessionId,
            displayId: generateDisplayId(sessionId, 'dm-'),
            participants: sessionParticipants,
            messages: [],
            threads: {},
            createdAt: sessionCreatedAt,
            unreadCount: 0,
            isUnreadCountPlaceholder: true,
            oldestFetchedMessageTimestamp: null,
            hasMoreOlderMessages: true,
            last_fetched_message_timestamp: null,
            oldestFetchedMessageId: null,
            newestFetchedMessageId: null,
          };
          setWorkspace(prev => {
            if (!prev) return null;
            const updatedWs = applyAddDirectMessageUpdateToWorkspace(prev, recoveredDm);
            return applySetCurrentDirectMessageToWorkspace(updatedWs, recoveredDm.id); // This sets current and handles nav history
          });
          return recoveredDm.id;
        }
      }
    } catch (e: any) {
      console.error('Unexpected error in addDirectMessage:', e);
      handleUnexpectedError(e, 'starting direct message');
      return null;
    }
  };

  const switchWorkspace = async (workspaceId: string): Promise<void> => {
    if (!authUser) {
      toast.error("Cannot switch workspace: User not authenticated.");
      return;
    }

    if (workspace?.id === workspaceId) {
      // Already on the target workspace
      return;
    }

    // Check if the target workspace exists in the user's workspace list
    const targetWorkspaceSummary = workspace?.userWorkspaces?.find(ws => ws.id === workspaceId);
    if (!targetWorkspaceSummary) {
      toast.error("Workspace not found or you don't have access to it.");
      return;
    }

    try {
      setWorkspaceLoading(true);

      // Fetch the full workspace data for the target workspace
      const targetWorkspaceData = await getInitialWorkspaceDataForUser(authUser.id, workspaceId);

      if (!targetWorkspaceData) {
        toast.error(`Failed to load workspace "${targetWorkspaceSummary.name}". Please try again.`);
        return;
      }

      // Preserve the userWorkspaces list from the current workspace
      const preservedUserWorkspaces = workspace?.userWorkspaces || [];

      // Update the workspace state with the new workspace data
      setWorkspace({
        ...targetWorkspaceData,
        userWorkspaces: preservedUserWorkspaces, // Preserve the list across switches
      });

      toast.success(`Switched to "${targetWorkspaceSummary.name}" workspace`);
    } catch (error: any) {
      console.error('Error switching workspace:', error);
      toast.error(`Failed to switch to "${targetWorkspaceSummary.name}". Please try again.`);
    } finally {
      setWorkspaceLoading(false);
    }
  };

  const updateChannel = async (updates: Partial<Channel> & Pick<Channel, 'id'>): Promise<void> => {
    if (!workspace || !authUser) {
      toast.error("Cannot update channel: Not authenticated or workspace not loaded.");
      return;
    }

    // Optimistically update the local state first for all properties in 'updates'.
    // This ensures the UI reflects changes immediately.
    // The applyUpdateChannelToWorkspace function handles merging Partial<Channel> into the existing channel state.
    setWorkspace(prev => {
      if (!prev) return null;
      const existingChannel = findChannelById(prev.sections, updates.id);
      if (!existingChannel) {
        console.warn(`[updateChannel] Channel with id ${updates.id} not found in state for optimistic update.`);
        return prev; // or handle error appropriately
      }
      // Create a full Channel object for the optimistic update
      const channelForOptimisticUpdate: Channel = { ...existingChannel, ...updates };
      return applyUpdateChannelToWorkspace(prev, channelForOptimisticUpdate);
    });

    // If channelNote is specifically part of the updates, persist it to Supabase.
    // The check `updates.channelNote !== undefined` correctly handles cases where
    // channelNote is being set to a new string, an empty string "", or null.
    if (updates.channelNote !== undefined) {
      try {
        const { error: supabaseError, data: supabaseData } = await supabase
          .from('channels')
          .update({ channel_note: updates.channelNote }) // DB column is channel_note
          .eq('id', updates.id)
          .select('id') // Select to confirm RLS allowed the update and to get feedback.
          .single(); // Ensures we expect one row or handle error if not.

        if (supabaseError) {
          handleSupabaseError(supabaseError, OPERATION_CONTEXTS.UPDATE_CHANNEL_NOTE, {
            operation: 'channel note update',
            fallbackMessage: 'Failed to save channel note to the server.'
          });
          // Note: The optimistic local update for channel_note is not reverted here.
          // If reversion is critical, further logic would be needed to store the previous note.
        } else if (!supabaseData) {
          // This case might occur if RLS silently prevented the update without throwing a PostgREST error,
          // or if the channel ID was somehow invalid but didn't cause an immediate error.
          console.warn(`Channel note update for ${updates.id} seemed to succeed but no data was returned from Supabase.`);
          toast.warning('Channel note may not have saved correctly. Please verify.');
        } else {
          // console.log(`Channel note for ${updates.id} (data: ${supabaseData?.id}) updated successfully on the server.`);
          // UI is already updated optimistically, so no success toast is typically needed here.
        }
      } catch (e: any) {
        handleUnexpectedError(e, 'channel note update');
        // Local state remains optimistically updated.
      }
    }
    // Other properties that might have been in 'updates' (e.g., name, description)
    // were optimistically applied. If they also need persistence, similar Supabase
    // calls would be required for them. This function now specifically handles
    // persistence for 'channel_note' as per the task.
  };

  const markConversationRead = (conversationId: string, type: 'channel' | 'dm') => {
    if (!workspace) return;

    // Determine the timestamp to use for marking as read
    let timestampToMark: string | undefined;
    let conversationObject: Channel | DirectMessage | null = null;

    if (type === 'channel') {
        conversationObject = findChannelById(workspace.sections, conversationId);
    } else {
        conversationObject = workspace.directMessages.find(dm => dm.id === conversationId) || null;
    }

    if (conversationObject) {
        if (conversationObject.messages && conversationObject.messages.length > 0) {
            // Find the maximum timestamp from the loaded messages by iterating through them
            timestampToMark = conversationObject.messages.reduce((maxTs, message) => {
                return new Date(message.timestamp) > new Date(maxTs) ? message.timestamp : maxTs;
            }, conversationObject.messages[0].timestamp); // Initialize with the first message's timestamp as a starting point
            // console.log(`[markConversationRead] Determined latest from loaded messages: ${timestampToMark} for ${conversationId} (type: ${type})`);
        } else {
            // If no messages loaded client-side, or messages array is empty,
            // use the conversation's last_message_timestamp from DB summary
            timestampToMark = conversationObject.lastMessageTimestamp;
            if (timestampToMark) {
                // console.log(`[markConversationRead] No/empty loaded messages, using DB lastMessageTimestamp: ${timestampToMark} for ${conversationId} (type: ${type})`);
            } else {
                // console.log(`[markConversationRead] No/empty loaded messages and no DB lastMessageTimestamp for ${conversationId} (type: ${type})`);
            }
        }
    } else {
        console.warn(`[markConversationRead] Conversation object not found for ID: ${conversationId}, type: ${type}. Cannot determine timestamp.`);
    }

    // If no timestamp could be determined (e.g., new channel, no messages, no DB timestamp yet),
    // use the current time.
    const finalTimestampToStore = timestampToMark || new Date().toISOString();

    // --- Idempotency Check & Early Exit (Issue 1 Fix) ---
    const puiEntry = persistentUnreadInfo[conversationId];
    const isAlreadyPersistentlyReadCorrectly = puiEntry &&
                                             puiEntry.unreadCount === 0 &&
                                             puiEntry.lastReadMessageTimestamp === finalTimestampToStore;

    let isAlreadyReadInWorkspace = false;
    if (type === 'channel') {
        const ch = findChannelById(workspace.sections, conversationId);
        if (ch && ch.unreadCount === 0) isAlreadyReadInWorkspace = true;
    } else { // dm
        const dm = workspace.directMessages.find(d => d.id === conversationId);
        if (dm && dm.unreadCount === 0) isAlreadyReadInWorkspace = true;
    }

    if (isAlreadyPersistentlyReadCorrectly && isAlreadyReadInWorkspace) {
      // console.log(`[Persistent Unread] SKIPPING mark ${type} ${conversationId}. Already up-to-date with timestamp ${finalTimestampToStore}.`);
      return; // Exit early, no log, no state updates.
    }
    // --- End Idempotency Check ---

    // Call Supabase RPC to mark conversation as read on the server
    if (authUser && workspace) { // Ensure user and workspace are available
      (async () => {
        try {
          const { error } = await supabase.rpc('mark_conversation_as_read', {
            p_conversation_id: conversationId,
            p_conversation_type: type,
            p_last_read_message_timestamp: finalTimestampToStore
          });

          if (error) {
            console.error(`[RPC Error] Failed to mark ${type} ${conversationId} as read on server:`, error);
            // Optionally, notify the user or implement retry logic if critical
          } else {
            // console.log(`[RPC Success] Successfully marked ${type} ${conversationId} as read on server up to ${finalTimestampToStore}`);
          }
        } catch (rpcException) {
          console.error(`[RPC Exception] Unexpected error calling mark_conversation_as_read for ${type} ${conversationId}:`, rpcException);
        }
      })();
    } else {
      console.warn(`[markConversationRead] Skipping RPC call because authUser or workspace is not available. User: ${!!authUser}, Workspace: ${!!workspace}`);
    }

    setConversationReadMarkers(prevMarkers => ({ ...prevMarkers, [conversationId]: finalTimestampToStore }));

    // Update workspace state (sets unreadCount to 0 in the main workspace object)
    setWorkspace(prev => {
      if (!prev) return null;
      return applyMarkConversationReadToWorkspace(prev, conversationId, type);
    });

    // Update persistentUnreadInfo
    setPersistentUnreadInfo(prevPui => {
      const currentPuiEntry = prevPui[conversationId];
      // Avoid redundant update if already correct (double check after other state updates)
      if (currentPuiEntry && currentPuiEntry.unreadCount === 0 && currentPuiEntry.lastReadMessageTimestamp === finalTimestampToStore) {
        return prevPui;
      }
     // console.log(`[Persistent Unread] Marked ${type} ${conversationId} as read. Last read timestamp: ${finalTimestampToStore}`);
     return {
       ...prevPui,
        [conversationId]: {
          unreadCount: 0,
          lastReadMessageTimestamp: finalTimestampToStore
        }
      };
    });
  };

  const userToUse = getCurrentUser();
  const effectiveMarkAsReadDelaySeconds = userToUse?.settings?.markAsReadDelaySecondsOverride ?? workspace.settings?.markAsReadDelaySecondsDefault ?? 5;

  const setAutoResetNewMessages = (value: boolean) => {
    if (!workspace || !authUser) return;
    const currentUserId = getCurrentUser().id;
    const currentUserSettings = getCurrentUser().settings || {};
    updateUserSetting(currentUserId, { ...currentUserSettings, autoResetNewMessagesOverride: value });
  };

  const updateUserSetting = async (userId: string, newSettings: UserSettings) => {
    if (!authUser) {
      toast.error("User not authenticated. Cannot update settings.");
      return;
    }
    // Ensure userId is the authenticated user's ID for security, RLS will also enforce this.
    if (userId !== authUser.id) {
        toast.error("Cannot update settings for another user.");
        return;
    }

    // Optimistic local update
    setWorkspace(prev => {
      if (!prev) return null;
      return applyUpdateUserSettingToWorkspace(prev, userId, newSettings);
    });

    // Persist to Supabase
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ settings: newSettings })
        .eq('id', userId);

      if (error) {
        console.error('Error updating user settings in Supabase:', error);
        toast.error(`Failed to save settings: ${error.message}`);
        // Consider reverting optimistic update here if needed
      } else {
        // console.log('User settings updated successfully in Supabase');
      }
    } catch (e) {
      console.error('Supabase call failed (settings):', e);
      toast.error('An unexpected error occurred while saving settings.');
    }
  };

  const updateWorkspaceSettings = async (workspaceId: string, newSettings: Partial<WorkspaceSettings>) => {
    // Check if current user is admin before proceeding
    if (!authUser) {
      toast.error("User not authenticated. Cannot update workspace settings.");
      throw new Error("User not authenticated");
    }

    const currentUser = workspace?.users.find(u => u.id === authUser.id);
    if (currentUser?.workspaceRole !== 'admin') {
      toast.error("Access denied: Only workspace administrators can modify workspace settings.");
      throw new Error("Insufficient privileges");
    }

    // Optimistic local update
    setWorkspace(prev => {
      if (!prev || prev.id !== workspaceId) return prev;
      return applyUpdateWorkspaceSettingsToWorkspace(prev, newSettings);
    });

    // Persist to Supabase
    try {
      const { error } = await supabase
        .from('workspaces')
        .update({ settings: newSettings })
        .eq('id', workspaceId);

      if (error) {
        console.error('Error updating workspace settings in Supabase:', error);

        // Handle specific permission errors using the centralized error handler
        handleSupabaseError(error, OPERATION_CONTEXTS.UPDATE_WORKSPACE_SETTINGS, {
          operation: 'workspace settings update',
          fallbackMessage: 'Failed to save workspace settings',
          showToast: true,
          logError: false // Already logged above
        });

        // Revert optimistic update on error
        setWorkspace(prev => {
          if (!prev || prev.id !== workspaceId) return prev;
          return prev; // Keep previous state
        });

        throw error;
      } else {
        // console.log('Workspace settings updated successfully in Supabase');
        // Success toast is handled in the component
      }
    } catch (e: any) {
      console.error('Supabase call failed (workspace settings):', e);

      // Handle unexpected errors using the centralized error handler
      handleUnexpectedError(e, 'workspace settings update');

      // Revert optimistic update on error
      setWorkspace(prev => {
        if (!prev || prev.id !== workspaceId) return prev;
        return prev; // Keep previous state
      });

      throw e;
    }
  };

  const updateUserStatus = async (userId: string, status: User['status'], statusMessage?: string) => {
    if (!authUser) {
      toast.error("User not authenticated. Cannot update status.");
      return;
    }
    // Ensure userId is the authenticated user's ID for security, RLS will also enforce this.
    if (userId !== authUser.id) {
        toast.error("Cannot update status for another user.");
        return;
    }

    // Optimistic local update
    setWorkspace(prev => {
      if (!prev) return null;
      return applyUpdateUserStatusToWorkspace(prev, userId, status, statusMessage);
    });

    // Persist to Supabase
    // Retrieve the current user details from the local workspace state to get title and about
    const currentUserInWorkspace = workspace?.users.find(u => u.id === userId);

    if (!currentUserInWorkspace) {
        // Fallback: only update status and status_message if user details aren't found locally
        // This might happen if workspace state is not fully populated yet, though less likely for an active user.
        toast.warning("Local user profile details not fully available; persisting status and message only.");
        try {
            const { error } = await supabase
              .from('profiles')
              .update({
                status: status,
                status_message: statusMessage ?? null,
              })
              .eq('id', userId);

            if (error) {
              console.error('Error updating user status (fallback):', error);
              toast.error(`Failed to save status: ${error.message}`);
            } else {
              // console.log('User status/status_message updated successfully in Supabase (fallback)');
            }
        } catch (e) {
            console.error('Supabase call failed (status fallback):', e);
            toast.error('An unexpected error occurred while saving status.');
        }
        return;
    }

    try {
      const updatePayload = {
        status: status,
        title: currentUserInWorkspace.title ?? null,
        about: currentUserInWorkspace.about ?? null,
        status_message: statusMessage ?? null,
      };

      const { error } = await supabase
        .from('profiles')
        .update(updatePayload)
        .eq('id', userId);

      if (error) {
        console.error('Error updating user profile details in Supabase:', error);
        toast.error(`Failed to save profile details: ${error.message}`);
        // Consider reverting optimistic update here if needed
      } else {
        // console.log('User profile details (status, title, about, status_message) updated successfully in Supabase');
      }
    } catch (e) {
      console.error('Supabase call failed (profile details):', e);
      toast.error('An unexpected error occurred while saving profile details.');
    }
  };

  const addSearchToHistory = (term: string) => {
    if (!term.trim()) return;
    setSearchHistory(prev => {
      const newHistory = [term, ...prev.filter(t => t !== term)].slice(0, 10);
      localStorage.setItem('searchHistory', JSON.stringify(newHistory));
      return newHistory;
    });
  };

  const clearSearchHistory = () => { setSearchHistory([]); localStorage.removeItem('searchHistory'); };

  const performSearch = (term: string) => {
    if (!term.trim() || !workspace) { setSearchResults([]); return; }
    const lowerCaseTerm = term.toLowerCase();
    const results: Message[] = [];
    workspace.sections.forEach(s => s.channels.forEach(c => {
      c.messages.forEach(m => { if (m.content.toLowerCase().includes(lowerCaseTerm)) results.push({ ...m, channelId: c.id }); });
      Object.values(c.threads).forEach(th => (th as Thread).messages.forEach(m => { if (m.content.toLowerCase().includes(lowerCaseTerm)) results.push({ ...m, channelId: c.id, threadId: th.id }); }));
    }));
    workspace.directMessages.forEach(dm => {
      dm.messages.forEach(m => { if (m.content.toLowerCase().includes(lowerCaseTerm)) results.push({ ...m, channelId: dm.id }); });
      Object.values(dm.threads).forEach(th => (th as Thread).messages.forEach(m => { if (m.content.toLowerCase().includes(lowerCaseTerm)) results.push({ ...m, channelId: dm.id, threadId: th.id }); }));
    });
    setSearchResults(results);
    if (results.length > 0) addSearchToHistory(term);
  };

  const clearSearchResults = () => setSearchResults([]);

  const canGoBack = currentHistoryIndex > 0;
  const canGoForward = currentHistoryIndex < navigationHistory.length - 1;

  const navigateBack = () => {
    if (canGoBack) {
      const newIndex = currentHistoryIndex - 1;
      const item = navigationHistory[newIndex];
      setCurrentHistoryIndex(newIndex);
      if (item.type === 'channel') setCurrentChannel(item.id, true); else setCurrentDirectMessage(item.id, true);
    }
  };

  const navigateForward = () => {
    if (canGoForward) {
      const newIndex = currentHistoryIndex + 1;
      const item = navigationHistory[newIndex];
      setCurrentHistoryIndex(newIndex);
      if (item.type === 'channel') setCurrentChannel(item.id, true); else setCurrentDirectMessage(item.id, true);
    }
  };

  const navigateTo = (item: NavigationItem) => {
    if (item.type === 'channel') setCurrentChannel(item.id, false); else setCurrentDirectMessage(item.id, false);
  };

  const recentConversations = navigationHistory.slice().reverse().filter((item, index, self) => index === self.findIndex((t) => t.id === item.id && t.type === item.type)).slice(0, 15);
  const showMemberProfile = (userId: string) => { setSelectedMemberId(userId); setMemberProfileDialogOpen(true); };

  const navigateToChannelTopic = (channelId: string, topicId: string) => {
    setCurrentChannel(channelId, false, true); // This already handles some state updates
    setWorkspace(prev => prev ? applyNavigateToChannelTopicToWorkspace(prev, channelId, topicId) : null);
    setCurrentChannelActiveView('Topics');
    if (isSearchViewActive) setIsSearchViewActive(false);
  };

  const clearActiveChannelTopicForChannel = (channelId: string) => {
    setWorkspace(prev => prev ? applyClearActiveChannelTopicToWorkspace(prev, channelId) : null);
  };


  const loadOlderMessages = async (conversationId: string, conversationType: 'channel' | 'dm') => {
    if (!workspace) return;

    let conversation: Channel | DirectMessage | undefined;
    if (conversationType === 'channel') {
      conversation = findChannelById(workspace.sections, conversationId);
    } else {
      conversation = workspace.directMessages.find(dm => dm.id === conversationId);
    }

    if (!conversation || !conversation.hasMoreOlderMessages) {
      return;
    }

    // Use oldestFetchedMessageTimestamp and oldestFetchedMessageId for cursor
    let cursorTimestamp = conversation.oldestFetchedMessageTimestamp;
    let cursorId = conversation.oldestFetchedMessageId;

    // Fallback if IDs are not set but timestamps are (e.g., from older state before IDs were added)
    if (!cursorId && cursorTimestamp && conversation.messages && conversation.messages.length > 0) {
        if (conversation.messages[0].timestamp === cursorTimestamp) {
            cursorId = conversation.messages[0].id;
        }
    }

    // If we still don't have cursor info, try to use the oldest message as fallback
    if (!cursorTimestamp && conversation.messages && conversation.messages.length > 0) {
      cursorTimestamp = conversation.messages[0].timestamp;
      cursorId = conversation.messages[0].id;
      console.log(`[LoadOlder] Using oldest message as cursor fallback: ${cursorTimestamp}, ${cursorId}`);
    }

    if (!cursorTimestamp) {
      console.log(`[LoadOlder] No cursor timestamp available for ${conversationType} ${conversationId}`);
      return;
    }
    try {
      let olderMessagesArray: Message[] | null = null;
      if (conversationType === 'channel') {
        olderMessagesArray = await getMessagesForChannel(conversationId, getOlderMessageFetchLimit(), cursorTimestamp, cursorId, undefined);
      } else {
        olderMessagesArray = await getMessagesForDirectMessage(conversationId, getOlderMessageFetchLimit(), cursorTimestamp, cursorId, undefined);
      }

      // olderMessagesArray from provider (when using olderThanTimestamp & cursorId) are NEWEST-FIRST.
      // Reverse to make them OLDEST-FIRST for prepending.
      const olderMessagesOldestFirst = olderMessagesArray ? olderMessagesArray.slice().reverse() : [];
      if (olderMessagesArray === null) {
        toast.error("Failed to load older messages. Please check your connection.");
        setWorkspace(prevWs => {
          if (!prevWs) return null;
          if (conversationType === 'channel') {
            return {
              ...prevWs,
              sections: prevWs.sections.map(s => ({
                ...s,
                channels: s.channels.map(ch => ch.id === conversationId ? { ...ch, hasMoreOlderMessages: false } : ch)
              }))
            };
          } else {
            return {
              ...prevWs,
              directMessages: prevWs.directMessages.map(dm => dm.id === conversationId ? { ...dm, hasMoreOlderMessages: false } : dm)
            };
          }
        });
        return;
      }

      setWorkspace(prevWs => {
        if (!prevWs || !prevWs.id) return prevWs;

        const numberOfNewlyFetchedOlderMessages = olderMessagesOldestFirst.length;
        const newHasMoreFlag = numberOfNewlyFetchedOlderMessages === getOlderMessageFetchLimit();

        if (conversationType === 'channel') {
          const sectionIndex = prevWs.sections.findIndex(s => s.channels.some(c => c.id === conversationId));
          if (sectionIndex === -1) return prevWs;

          const channelIndex = prevWs.sections[sectionIndex].channels.findIndex(c => c.id === conversationId);
          if (channelIndex === -1) return prevWs;

          const currentChannel = prevWs.sections[sectionIndex].channels[channelIndex];

          const combinedMessages = [...olderMessagesOldestFirst, ...currentChannel.messages];
          const uniqueMessages = Array.from(new Map(combinedMessages.map(msg => [msg.id, msg])).values())
                                          .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

          const newOldestTs = uniqueMessages.length > 0 ? uniqueMessages[0].timestamp : currentChannel.oldestFetchedMessageTimestamp;
          const newOldestId = uniqueMessages.length > 0 ? uniqueMessages[0].id : currentChannel.oldestFetchedMessageId;

          const currentOverallNewestTimestamp = uniqueMessages.length > 0 ? uniqueMessages[uniqueMessages.length -1].timestamp : currentChannel.lastMessageTimestamp;
          const currentOverallNewestId = uniqueMessages.length > 0 ? uniqueMessages[uniqueMessages.length -1].id : currentChannel.newestFetchedMessageId;
          const updatedChannel = {
            ...currentChannel,
            messages: uniqueMessages,
            oldestFetchedMessageTimestamp: newOldestTs,
            oldestFetchedMessageId: newOldestId,
            hasMoreOlderMessages: newHasMoreFlag,
            lastMessageTimestamp: currentOverallNewestTimestamp,
            newestFetchedMessageId: currentOverallNewestId, // Ensure newest ID is also updated if messages changed
            last_fetched_message_timestamp: currentChannel.last_fetched_message_timestamp || (uniqueMessages.length > 0 ? uniqueMessages[uniqueMessages.length - 1].timestamp : currentChannel.last_fetched_message_timestamp),
          };

          if (!currentChannel.last_fetched_message_timestamp && uniqueMessages.length > 0) {
            const newTimestamp = uniqueMessages[uniqueMessages.length - 1].timestamp;
            const newId = uniqueMessages[uniqueMessages.length - 1].id;
            localStorage.setItem(`last_fetched_ts_${conversationId}`, newTimestamp);
            localStorage.setItem(`last_fetched_id_${conversationId}`, newId);
          }

          const updatedChannels = [
            ...prevWs.sections[sectionIndex].channels.slice(0, channelIndex),
            updatedChannel,
            ...prevWs.sections[sectionIndex].channels.slice(channelIndex + 1),
          ];

          const updatedSection = {
            ...prevWs.sections[sectionIndex],
            channels: updatedChannels,
          };

          const updatedSections = [
            ...prevWs.sections.slice(0, sectionIndex),
            updatedSection,
            ...prevWs.sections.slice(sectionIndex + 1),
          ];

          return {
            ...prevWs,
            sections: updatedSections,
          };

        } else { // Direct Message
          const dmIndex = prevWs.directMessages.findIndex(d => d.id === conversationId);
          if (dmIndex === -1) return prevWs;

          const currentDm = prevWs.directMessages[dmIndex];

          const combinedMessages = [...olderMessagesOldestFirst, ...currentDm.messages];
          const uniqueMessages = Array.from(new Map(combinedMessages.map(msg => [msg.id, msg])).values())
                                        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

          const newOldestTs = uniqueMessages.length > 0 ? uniqueMessages[0].timestamp : currentDm.oldestFetchedMessageTimestamp;
          const newOldestId = uniqueMessages.length > 0 ? uniqueMessages[0].id : currentDm.oldestFetchedMessageId;

          const currentOverallNewestTimestamp = uniqueMessages.length > 0 ? uniqueMessages[uniqueMessages.length -1].timestamp : currentDm.lastMessageTimestamp;
          const currentOverallNewestId = uniqueMessages.length > 0 ? uniqueMessages[uniqueMessages.length -1].id : currentDm.newestFetchedMessageId;
          const updatedDm = {
            ...currentDm,
            messages: uniqueMessages,
            oldestFetchedMessageTimestamp: newOldestTs,
            oldestFetchedMessageId: newOldestId,
            hasMoreOlderMessages: newHasMoreFlag,
            lastMessageTimestamp: currentOverallNewestTimestamp,
            newestFetchedMessageId: currentOverallNewestId, // Ensure newest ID is also updated
            last_fetched_message_timestamp: currentDm.last_fetched_message_timestamp || (uniqueMessages.length > 0 ? uniqueMessages[uniqueMessages.length - 1].timestamp : currentDm.last_fetched_message_timestamp),
          };

          if (!currentDm.last_fetched_message_timestamp && uniqueMessages.length > 0) {
            const newTimestamp = uniqueMessages[uniqueMessages.length - 1].timestamp;
            const newId = uniqueMessages[uniqueMessages.length - 1].id;
            localStorage.setItem(`last_fetched_ts_${conversationId}`, newTimestamp);
            localStorage.setItem(`last_fetched_id_${conversationId}`, newId);
          }

          const updatedDirectMessages = [
            ...prevWs.directMessages.slice(0, dmIndex),
            updatedDm,
            ...prevWs.directMessages.slice(dmIndex + 1),
          ];

          return {
            ...prevWs,
            directMessages: updatedDirectMessages,
          };
        }
      });
    } catch (error) {
      console.error(`Error loading older messages for ${conversationType} ${conversationId}:`, error);
    }
  };

  const updateUserProfile = async (userId: string, profileData: { name?: string; title?: string; about?: string; avatar?: string }) => {
    if (!workspace) return;

    // Optimistic local update
    setWorkspace(prev => {
      if (!prev) return null;

      const updatedUsers = prev.users.map(user => {
        if (user.id === userId) {
          return {
            ...user,
            ...(profileData.name !== undefined && { name: profileData.name }),
            ...(profileData.title !== undefined && { title: profileData.title }),
            ...(profileData.about !== undefined && { about: profileData.about }),
            ...(profileData.avatar !== undefined && { avatar: profileData.avatar }),
          };
        }
        return user;
      });

      return { ...prev, users: updatedUsers };
    });

    // Persist to Supabase
    try {
      const updatePayload: any = {};
      if (profileData.name !== undefined) updatePayload.name = profileData.name;
      if (profileData.title !== undefined) updatePayload.title = profileData.title;
      if (profileData.about !== undefined) updatePayload.about = profileData.about;
      if (profileData.avatar !== undefined) updatePayload.avatar = profileData.avatar;

      const { error } = await supabase
        .from('profiles')
        .update(updatePayload)
        .eq('id', userId);

      if (error) {
        console.error('Error updating user profile in Supabase:', error);
        toast.error(`Failed to save profile: ${error.message}`);
        // TODO: Consider reverting optimistic update here if needed
      } else {
        toast.success('Profile updated successfully!');
      }
    } catch (e) {
      console.error('Supabase call failed (profile update):', e);
      toast.error('An unexpected error occurred while saving profile.');
    }
  };

  const createChannelTopic = async (channelId: string, title: string, summary?: string): Promise<ChannelTopic | null> => {
    if (!workspace || !authUser) {
      toast.error("Cannot create topic: Not authenticated or workspace not loaded.");
      return null;
    }
    try {
      const { data: newTopic, error } = await supabase.rpc('create_channel_topic', {
        p_channel_id: channelId,
        p_title: title,
        p_summary: summary || null, // Ensure summary is null if undefined
      });

      if (error) {
        handleSupabaseError(error, OPERATION_CONTEXTS.CREATE_CHANNEL_TOPIC, {
          operation: 'channel topic creation',
          fallbackMessage: 'Failed to create channel topic'
        });
        return null;
      }

      if (!newTopic) {
        toast.error('Failed to create channel topic: No data returned from server.');
        return null;
      }

      setWorkspace(prevWs => {
        if (!prevWs) return null;
        const updatedWs = JSON.parse(JSON.stringify(prevWs)) as Workspace;
        const section = updatedWs.sections.find(s => s.channels.some(c => c.id === channelId));
        if (section) {
          const channel = section.channels.find(c => c.id === channelId);
          if (channel) {
            if (!channel.channelTopics) {
              channel.channelTopics = [];
            }
            // The RPC returns a single object, not an array
            const topicToAdd = newTopic as ChannelTopic;
            channel.channelTopics.push(topicToAdd);
          }
        }
        return updatedWs;
      });
      toast.success(`Topic "${newTopic.title}" created!`);
      return newTopic as ChannelTopic;
    } catch (e: any) {
      handleUnexpectedError(e, 'channel topic creation');
      return null;
    }
  };

  const updateChannelTopic = async (topicId: string, updates: { title?: string; summary?: string }): Promise<ChannelTopic | null> => {
    if (!workspace || !authUser) {
      toast.error("Cannot update topic: Not authenticated or workspace not loaded.");
      return null;
    }
    try {
      const { data: updatedTopic, error } = await supabase.rpc('update_channel_topic', {
        p_topic_id: topicId,
        p_title: updates.title,
        p_summary: updates.summary,
      });

      if (error) {
        handleSupabaseError(error, OPERATION_CONTEXTS.UPDATE_CHANNEL_TOPIC, {
          operation: 'channel topic update',
          fallbackMessage: 'Failed to update channel topic'
        });
        return null;
      }

      if (!updatedTopic) {
        toast.error('Failed to update channel topic: No data returned from server.');
        return null;
      }
      const topicToUpdate = updatedTopic as ChannelTopic;

      setWorkspace(prevWs => {
        if (!prevWs) return null;
        const updatedWs = JSON.parse(JSON.stringify(prevWs)) as Workspace;
        let found = false;
        for (const section of updatedWs.sections) {
          for (const channel of section.channels) {
            if (channel.channelTopics) {
              const topicIndex = channel.channelTopics.findIndex(t => t.id === topicId);
              if (topicIndex !== -1) {
                channel.channelTopics[topicIndex] = { ...channel.channelTopics[topicIndex], ...topicToUpdate };
                found = true;
                break;
              }
            }
          }
          if (found) break;
        }
        return updatedWs;
      });
      toast.success(`Topic "${topicToUpdate.title}" updated!`);
      return topicToUpdate;
    } catch (e: any) {
      handleUnexpectedError(e, 'channel topic update');
      return null;
    }
  };

  const deleteChannelTopic = async (topicId: string, channelId: string): Promise<boolean> => {
    if (!workspace || !authUser) {
      toast.error("Cannot delete topic: Not authenticated or workspace not loaded.");
      return false;
    }
    try {
      const { error } = await supabase.rpc('delete_channel_topic', { p_topic_id: topicId });

      if (error) {
        handleSupabaseError(error, OPERATION_CONTEXTS.DELETE_CHANNEL_TOPIC, {
          operation: 'channel topic deletion',
          fallbackMessage: 'Failed to delete channel topic'
        });
        return false;
      }

      setWorkspace(prevWs => {
        if (!prevWs) return null;
        const updatedWs = JSON.parse(JSON.stringify(prevWs)) as Workspace;
        const section = updatedWs.sections.find(s => s.channels.some(c => c.id === channelId));
        if (section) {
          const channel = section.channels.find(c => c.id === channelId);
          if (channel && channel.channelTopics) {
            channel.channelTopics = channel.channelTopics.filter(t => t.id !== topicId);
          }
        }
        return updatedWs;
      });
      toast.success(`Topic deleted successfully!`);
      return true;
    } catch (e: any) {
      handleUnexpectedError(e, 'channel topic deletion');
      return false;
    }
  };

  const archiveChannelTopic = async (topicId: string, channelId: string): Promise<ChannelTopic | null> => {
    if (!workspace || !authUser) {
      toast.error("Cannot archive topic: Not authenticated or workspace not loaded.");
      return null;
    }
    try {
      const { data: archivedTopicData, error } = await supabase.rpc('archive_channel_topic', { p_topic_id: topicId });

      if (error) {
        handleSupabaseError(error, OPERATION_CONTEXTS.ARCHIVE_CHANNEL_TOPIC, {
          operation: 'channel topic archival',
          fallbackMessage: 'Failed to archive channel topic'
        });
        return null;
      }
      if (!archivedTopicData) {
        toast.error('Failed to archive channel topic: No data returned from server.');
        return null;
      }
      const archivedTopic = archivedTopicData as ChannelTopic;


      setWorkspace(prevWs => {
        if (!prevWs) return null;
        const updatedWs = JSON.parse(JSON.stringify(prevWs)) as Workspace;
        const section = updatedWs.sections.find(s => s.channels.some(c => c.id === channelId));
        if (section) {
          const channel = section.channels.find(c => c.id === channelId);
          if (channel && channel.channelTopics) {
            const topicIndex = channel.channelTopics.findIndex(t => t.id === topicId);
            if (topicIndex !== -1) {
              // The RPC returns the full updated topic, including server-set archived_at and archived_by
              channel.channelTopics[topicIndex] = archivedTopic;
            }
          }
        }
        return updatedWs;
      });
      toast.success(`Topic "${archivedTopic.title}" archived!`);
      return archivedTopic;
    } catch (e: any) {
      handleUnexpectedError(e, 'channel topic archival');
      return null;
    }
  };

  const unarchiveChannelTopic = async (topicId: string, channelId: string): Promise<ChannelTopic | null> => {
    if (!workspace || !authUser) {
      toast.error("Cannot unarchive topic: Not authenticated or workspace not loaded.");
      return null;
    }
    try {
      const { data: unarchivedTopicData, error } = await supabase.rpc('unarchive_channel_topic', { p_topic_id: topicId });

      if (error) {
        handleSupabaseError(error, OPERATION_CONTEXTS.UNARCHIVE_CHANNEL_TOPIC, {
          operation: 'channel topic unarchival',
          fallbackMessage: 'Failed to unarchive channel topic'
        });
        return null;
      }
      if (!unarchivedTopicData) {
        toast.error('Failed to unarchive channel topic: No data returned from server.');
        return null;
      }
      const unarchivedTopic = unarchivedTopicData as ChannelTopic;

      setWorkspace(prevWs => {
        if (!prevWs) return null;
        const updatedWs = JSON.parse(JSON.stringify(prevWs)) as Workspace;
        const section = updatedWs.sections.find(s => s.channels.some(c => c.id === channelId));
        if (section) {
          const channel = section.channels.find(c => c.id === channelId);
          if (channel && channel.channelTopics) {
            const topicIndex = channel.channelTopics.findIndex(t => t.id === topicId);
            if (topicIndex !== -1) {
              // The RPC returns the full updated topic
              channel.channelTopics[topicIndex] = unarchivedTopic;
            }
          }
        }
        return updatedWs;
      });
      toast.success(`Topic "${unarchivedTopic.title}" unarchived!`);
      return unarchivedTopic;
    } catch (e: any) {
      handleUnexpectedError(e, 'channel topic unarchival');
      return null;
    }
  };

  const createWorkspace = async (name: string, iconUrl?: string): Promise<Workspace | null> => {
    if (!authUser || !workspace) {
      toast.error("User not authenticated or workspace context not available.");
      return null;
    }
    if (!name.trim()) {
      toast.error("Workspace name cannot be empty.");
      return null;
    }

    try {
      const { data: newWorkspaceData, error } = await supabase.rpc('create_workspace', {
        p_name: name.trim(),
        p_icon_url: iconUrl || null,
      });

      if (error) {
        handleSupabaseError(error, OPERATION_CONTEXTS.CREATE_WORKSPACE, {
          operation: 'workspace creation',
          fallbackMessage: 'Failed to create workspace',
        });
        return null;
      }

      if (!newWorkspaceData) {
        toast.error('Failed to create workspace: No data returned from server.');
        return null;
      }

      // The RPC returns a single workspace object.
      // We need to integrate this into the user's list of workspaces.
      // For now, this app context handles a single workspace at a time.
      // If the app were to support multiple workspaces simultaneously in the context,
      // we would add it to a list. Here, we might want to "switch" to this new workspace.
      // However, the current setup loads one workspace at a time via getInitialWorkspaceDataForUser.

      // For simplicity in this step, we'll add it to the *current* workspace object's structure
      // if we imagine a scenario where `workspaces` is a list in `AppContext`.
      // Since it's not, we'll log it and consider how to refresh or switch.
      // A practical approach would be to re-fetch user's workspaces or navigate.

      // For now, let's assume the RPC returns a complete Workspace object
      // that can be set as the current one, or added to a list if the app supported it.
      // The task asks to "Add this new workspace to the local `workspaces` list".
      // Since `workspace` state is singular, we'll update it.
      // This might be an oversimplification if the user has multiple workspaces they can switch between.
      // Let's assume for now the user is creating their *first* workspace or *another* one,
      // and we'll make it the active one.

      // The RPC returns the new workspace, let's assume it's compatible with our `Workspace` type.
      // We need to ensure the `currentUserId` is set correctly, and potentially `users` array.
      // The RPC makes the caller an admin.

      const newWorkspace = newWorkspaceData as Workspace; // Cast, assuming RPC returns compatible structure

      // Simulate adding to a list and setting as current (if AppContext supported a list)
      // For now, we'll just set it as the current workspace. This might require a full app reload
      // or a more sophisticated state update to truly "switch" workspaces.
      // A simple way is to update the `workspace` state and then perhaps trigger a re-fetch or navigation.

      setWorkspace(prevActiveWorkspace => {
        // newWorkspace here is the workspace object returned by the RPC
        const newWsSummaryEntry: Pick<Workspace, 'id' | 'name' | 'iconUrl'> = {
          id: newWorkspace.id,
          name: newWorkspace.name,
          iconUrl: newWorkspace.iconUrl,
        };

        const baseUserWorkspaces = prevActiveWorkspace?.userWorkspaces || [];
        // Add new workspace summary, ensuring no duplicates by ID (new one takes precedence)
        const updatedUserWorkspacesList = [
          ...baseUserWorkspaces.filter(ws => ws.id !== newWorkspace.id),
          newWsSummaryEntry,
        ];

        // The new active workspace state: starts with RPC data, then ensures client defaults
        const newActiveWorkspaceState: Workspace = {
          ...newWorkspace, // All data for the new workspace from RPC

          // Ensure the current authenticated user is always present and an admin in the new workspace's user list
          users: (() => {
            if (!authUser) return newWorkspace.users || []; // Should not happen if createWorkspace is called

            const rpcUsers = newWorkspace.users || [];
            const currentUserAsAdminData: WorkspaceDisplayUser = {
              id: authUser.id, // Critical: use authUser.id
              name: authProfile?.name || authUser.email || `User ${authUser.id.substring(0,4)}`,
              avatar: authProfile?.avatar || authUser.user_metadata?.avatar_url,
              workspaceRole: 'admin', // Explicitly admin
              status: authProfile?.status || 'offline',
              statusMessage: authProfile?.statusMessage, // Corrected to camelCase
              title: authProfile?.title,
              about: authProfile?.about,
              settings: authProfile?.settings,
            };

            // Add users from RPC, excluding current user, then add/overwrite current user with admin role
            return [
              ...rpcUsers.filter(u => u.id !== authUser.id),
              currentUserAsAdminData
            ];
          })(),
          sections: newWorkspace.sections || [],
          directMessages: newWorkspace.directMessages || [],
          currentUserId: authUser.id,
          currentSectionId: null, // Default for a new workspace
          currentChannelId: null, // Default for a new workspace
          currentDirectMessageId: null, // Default for a new workspace
          activeThreadId: null, // Default for a new workspace

          // The updated list of all workspaces the user belongs to
          userWorkspaces: updatedUserWorkspacesList,

          // Settings for the new workspace (from RPC or defaults)
          settings: newWorkspace.settings || {},
        };

        return newActiveWorkspaceState;
      });

      toast.success(`Workspace "${newWorkspace.name}" created successfully!`);
      // Potentially navigate or set as current.
      // For now, returning the new workspace. The UI can decide what to do.
      return newWorkspace;

    } catch (e: any) {
      handleUnexpectedError(e, 'workspace creation');
      return null;
    }
  };


  const value: AppContextType = {
    workspace, currentSection, currentChannel, currentDirectMessage, currentThread,
    setCurrentSection, setCurrentChannel, setCurrentDirectMessage, setActiveThread,
    sendMessage, getCurrentUser, toggleSidebar, isSidebarOpen, addReaction,
    addChannel, addSection, updateSection, deleteSection, addDirectMessage, switchWorkspace, updateChannel,
    conversationReadMarkers, markConversationRead, effectiveMarkAsReadDelaySeconds,
    updateUserSetting, workspaceSettings: workspace.settings, updateWorkspaceSettings,
    updateUserStatus, updateUserProfile, searchHistory, searchResults, performSearch, addSearchToHistory,
    clearSearchHistory, clearSearchResults, navigationHistory, currentHistoryIndex,
    canGoBack, canGoForward, navigateBack, navigateForward, navigateTo, recentConversations,
    isSearchViewActive, setIsSearchViewActive, showMemberProfile, navigateToChannelTopic,
    currentChannelActiveView, setCurrentChannelActiveView, clearActiveChannelTopicForChannel,
    autoResetNewMessages, setAutoResetNewMessages,
    persistentUnreadInfo, setPersistentUnreadInfo,
    loadOlderMessages,
    fetchNewerMessagesForConversation,
    createChannelTopic,
    updateChannelTopic,
    deleteChannelTopic,
    archiveChannelTopic,
    unarchiveChannelTopic,
    createWorkspace,
  };

  // When returning to the tab, show the app with a loading overlay
  // if (loadingAfterReturn) {
  //   return (
  //     <>
  //       <AppContext.Provider value={value}>
  //         {children}
  //         <MemberProfileDialog member={selectedMember} open={memberProfileDialogOpen} onOpenChange={setMemberProfileDialogOpen} />
  //       </AppContext.Provider>
  //
  //       {/* Overlay with a subtle spinner */}
  //       <AppLoadingScreen variant="minimal" />
  //     </>
  //   );
  // }

  // Normal render
  return (
    <AppContext.Provider value={value}>
      {children}
      <MemberProfileDialog member={selectedMember} open={memberProfileDialogOpen} onOpenChange={setMemberProfileDialogOpen} />
    </AppContext.Provider>
  );
}

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) throw new Error('useApp must be used within an AppProvider');
  return context;
};

// deduplicateDirectMessages is now imported from app-context-utils.ts
